import os
import yaml
import warnings
import json
import sys
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from crewai.utilities.events import (
    CrewKickoffCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    LLMStreamChunkEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener

# --- 屏蔽所有警告信息 ---
warnings.filterwarnings("ignore")
# 重定向 stderr 以屏蔽 pydantic 警告


class SuppressStderr:
    def __enter__(self):
        self._original_stderr = sys.stderr
        sys.stderr = open(os.devnull, 'w')
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stderr.close()
        sys.stderr = self._original_stderr


# 加载环境变量
load_dotenv()

# --- 事件监听器定义 (V5 - Final) ---


class K8sTestStreamingListener(BaseEventListener):
    """
    监听 CrewAI 事件并提供简洁的、用户友好的流式输出。
    """

    def __init__(self):
        super().__init__()
        self._thought_buffer = ""
        self._current_step = 0

    def setup_listeners(self, crewai_event_bus):
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_chunk(_, event):
            if hasattr(event, 'chunk') and isinstance(event.chunk, str):
                self._thought_buffer += event.chunk

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(_, event):
            try:
                self._current_step += 1

                # 显示思考过程
                if self._thought_buffer:
                    thought_lines = self._thought_buffer.strip().split('\n')
                    clean_thought = []
                    for line in thought_lines:
                        if line.strip() and not line.startswith('Action:') and not line.startswith('Action Input:'):
                            clean_thought.append(line.strip())

                    if clean_thought:
                        print(f"\n🔍 **步骤 {self._current_step} - 分析思路:**")
                        for thought in clean_thought:
                            print(f"   💭 {thought}")

                    self._thought_buffer = ""

                # 显示工具调用 - 修复属性访问
                tool_name = getattr(event, 'tool_name', 'unknown_tool')
                print(f"\n🛠️  **调用工具:** `{tool_name}`")

            except Exception as e:
                # 静默处理错误，避免干扰用户体验
                pass

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(_, event):
            try:
                print(f"✅ **工具输出:**")
                print("---")
                # 格式化输出，使其更易读
                output = str(event.output)
                if len(output) > 1000:
                    # 如果输出太长，进行适当截断
                    print(output[:1000] + "\n... (输出已截断)")
                else:
                    print(output)
                print("---")
            except Exception as e:
                print("✅ **工具执行完成**")

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(_, event):
            print(f"\n🎉 **诊断任务完成!**")
            print(f"\n**最终诊断结果:**")
            print("---")
            print(event.output)
            print("---")


qwen_llm = LLM(
    model="openai/qwen-turbo-latest",
    base_url=os.getenv("QWEN_BASE_URL"),
    api_key=os.getenv("QWEN_API_KEY"),
    temperature=0.3,
    # 启用流式输出以配合事件监听器
    stream=True,
)


def load_yaml(path: str) -> dict:
    """加载并解析 YAML 文件"""
    with open(path, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)


def get_inputs() -> dict:
    """获取用户输入或从环境变量加载"""
    namespace = os.getenv("TEST_NAMESPACE", "default")
    pod_name = os.getenv("TEST_POD_NAME", "")
    question = (
        f"请在命名空间 '{namespace}' 中检查所有 Pod 的状态。"
        if not pod_name
        else f"请在命名空间 '{namespace}' 中详细描述 Pod '{pod_name}' 的状态。"
    )

    inputs = {
        "namespace": namespace,
        "pod_name": pod_name,
        "question": question,
    }
    return inputs


def run_k8s_test():
    """运行 Kubernetes 诊断专家测试"""
    print("🚀 开始运行 Kubernetes 诊断专家测试...")

    # 实例化新的监听器
    K8sTestStreamingListener()

    # MCP 服务器配置 (Kubernetes 工具服务器)
    mcp_server_config = {
        "url": "http://localhost:3000/sse",
        "transport": "sse"
    }

    # 加载 Agent 和 Task 配置
    try:
        with SuppressStderr():  # 屏蔽加载时的警告
            agents_config = load_yaml('config/agents.yaml')
            tasks_config = load_yaml('config/tasks.yaml')
    except FileNotFoundError as e:
        print(f"❌ 错误：配置文件未找到 - {e}")
        print("请确保 'config/agents.yaml' 和 'config/tasks.yaml' 文件存在于测试目录中。")
        return

    # 使用 MCP Server Adapter 连接并获取工具
    try:
        with SuppressStderr():  # 屏蔽连接时的警告
            with MCPServerAdapter(mcp_server_config) as mcp_tools:
                print("✅ 成功连接到 MCP 服务器。")

                available_tools = [tool.name for tool in mcp_tools]
                print(f"🛠️ 可用工具: {available_tools}")

                # 定义诊断专用工具（只读/分析工具）
                diagnostic_tool_names = [
                    "kubectl_get",          # 获取资源状态
                    "kubectl_describe",     # 获取详细信息和事件
                    "kubectl_logs",         # 查看日志
                    "explain_resource",     # 解释资源字段
                    "kubectl_context",      # 查看当前上下文
                    "list_api_resources",   # 列出可用资源类型
                    "ping"                  # 连通性测试
                ]

                # 检查核心诊断工具是否可用
                required_tools = ["kubectl_get",
                                  "kubectl_describe", "kubectl_logs"]
                missing_required = [
                    tool for tool in required_tools if tool not in available_tools]

                if missing_required:
                    print("❌ 错误：缺少必需的诊断工具。")
                    print(f"缺少: {missing_required}")
                    return

                # 收集可用的诊断工具（只包含诊断相关的工具）
                diagnostic_tools = []
                available_diagnostic_tools = []

                for tool_name in diagnostic_tool_names:
                    if tool_name in available_tools and tool_name in mcp_tools:
                        diagnostic_tools.append(mcp_tools[tool_name])
                        available_diagnostic_tools.append(tool_name)

                print(f"🔍 诊断工具: {available_diagnostic_tools}")

                # 检查是否有修复工具被意外包含
                modification_tools = ["kubectl_apply", "kubectl_delete", "kubectl_create",
                                      "kubectl_scale", "kubectl_patch", "kubectl_rollout"]
                detected_mod_tools = [
                    tool for tool in modification_tools if tool in available_tools]
                if detected_mod_tools:
                    print(f"ℹ️  检测到修复工具: {detected_mod_tools} (诊断阶段不使用)")

                # 创建 k8s_diagnoser Agent
                k8s_diagnoser_config = agents_config['k8s_diagnoser']
                k8s_diagnoser = Agent(
                    **k8s_diagnoser_config,
                    tools=diagnostic_tools,  # 使用所有可用工具
                    llm=qwen_llm,
                    verbose=False,  # 由监听器接管输出
                )

                # 创建测试任务
                test_task_config = tasks_config['k8s_diagnostic_test_task']
                test_task = Task(
                    **test_task_config,
                    agent=k8s_diagnoser
                )

                # 组建并启动 Crew
                crew = Crew(
                    agents=[k8s_diagnoser],
                    tasks=[test_task],
                    process=Process.sequential,
                    verbose=False  # 由监听器接管输出
                )

                # 获取输入并启动任务
                inputs = get_inputs()
                print("\n📋 开始执行诊断任务...")
                print(f"   - 问题: {inputs['question']}")

                with SuppressStderr():  # 屏蔽执行时的警告
                    crew.kickoff(inputs=inputs)

    except Exception as e:
        print(f"❌ 连接 MCP 服务器时发生错误: {e}")
        print("请确保 MCP 服务器正在运行并且可以访问。")


if __name__ == "__main__":
    run_k8s_test()
