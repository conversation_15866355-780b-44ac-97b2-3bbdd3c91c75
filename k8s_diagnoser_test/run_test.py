import os
import yaml
import warnings
import json
import sys
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from crewai.utilities.events import (
    CrewKickoffCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    LLMStreamChunkEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener

# --- 屏蔽所有警告信息 ---
warnings.filterwarnings("ignore")
# 重定向 stderr 以屏蔽 pydantic 警告


class SuppressStderr:
    def __enter__(self):
        self._original_stderr = sys.stderr
        sys.stderr = open(os.devnull, 'w')
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stderr.close()
        sys.stderr = self._original_stderr


# 加载环境变量
load_dotenv()

# --- 事件监听器定义 (V5 - Final) ---


class K8sTestStreamingListener(BaseEventListener):
    """优化的 ReAct 流程显示监听器"""

    def __init__(self):
        super().__init__()
        self._thought_buffer = ""
        self._current_step = 0

    def setup_listeners(self, crewai_event_bus):
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_chunk(_, event):
            if hasattr(event, 'chunk') and isinstance(event.chunk, str):
                self._thought_buffer += event.chunk

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(_, event):
            self._current_step += 1

            # 提取并显示完整的思考过程
            if self._thought_buffer:
                # 查找 Thought: 开始的内容
                lines = self._thought_buffer.strip().split('\n')
                thought_content = ""

                for line in lines:
                    if line.startswith('Thought:'):
                        thought_content = line[8:].strip()  # 去掉 "Thought:" 前缀
                        break

                if thought_content and len(thought_content) > 20:
                    print(f"\n🧠 **步骤 {self._current_step}**")
                    print(f"   💭 思考: {thought_content}")

                self._thought_buffer = ""

            # 显示工具执行
            tool_name = getattr(event, 'tool_name', 'unknown_tool')
            print(f"   🔧 执行: {tool_name}")

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(_, event):
            # 显示观察结果（工具输出摘要）
            if hasattr(event, 'output'):
                output = str(event.output)
                if 'Failed' in output or 'Error' in output or 'OOM' in output:
                    print(f"   👁️ 观察: 发现异常状态")
                elif len(output) > 100:
                    print(f"   👁️ 观察: 获取到详细信息")
                else:
                    print(f"   👁️ 观察: 数据收集完成")

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(_, event):
            print(f"\n{'='*50}")
            print(f"🎯 **SMART 诊断结果**")
            print(f"{'='*50}")
            print(event.output)
            print(f"{'='*50}")


qwen_llm = LLM(
    model="openai/qwen-turbo-latest",
    base_url=os.getenv("QWEN_BASE_URL"),
    api_key=os.getenv("QWEN_API_KEY"),
    temperature=0.3,
    # 启用流式输出以配合事件监听器
    stream=True,
)


def load_yaml(path: str) -> dict:
    """加载并解析 YAML 文件"""
    with open(path, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)


def get_inputs() -> dict:
    """获取用户输入或从环境变量加载"""
    namespace = os.getenv("TEST_NAMESPACE", "default")
    pod_name = os.getenv("TEST_POD_NAME", "")
    question = (
        f"请在命名空间 '{namespace}' 中检查所有 Pod 的状态。"
        if not pod_name
        else f"请在命名空间 '{namespace}' 中详细描述 Pod '{pod_name}' 的状态。"
    )

    inputs = {
        "namespace": namespace,
        "pod_name": pod_name,
        "question": question,
    }
    return inputs


def run_k8s_test():
    """运行 Kubernetes 诊断专家测试"""
    print("🚀 开始运行 Kubernetes 诊断专家测试...")

    # 实例化新的监听器
    K8sTestStreamingListener()

    # MCP 服务器配置 (Kubernetes 工具服务器)
    mcp_server_config = {
        "url": "http://localhost:3000/sse",
        "transport": "sse"
    }

    # 加载 Agent 和 Task 配置
    try:
        with SuppressStderr():  # 屏蔽加载时的警告
            agents_config = load_yaml('config/agents.yaml')
            tasks_config = load_yaml('config/tasks.yaml')
    except FileNotFoundError as e:
        print(f"❌ 错误：配置文件未找到 - {e}")
        print("请确保 'config/agents.yaml' 和 'config/tasks.yaml' 文件存在于测试目录中。")
        return

    # 连接 MCP 服务器并执行诊断
    with SuppressStderr():
        with MCPServerAdapter(mcp_server_config) as mcp_tools:
            print("✅ 连接到 MCP 服务器")

            available_tools = [tool.name for tool in mcp_tools]

            # 诊断工具列表
            diagnostic_tool_names = [
                "kubectl_get", "kubectl_describe", "kubectl_logs",
                "explain_resource", "kubectl_context", "list_api_resources", "ping"
            ]

            # 收集可用的诊断工具
            diagnostic_tools = []
            for tool_name in diagnostic_tool_names:
                if tool_name in available_tools:
                    diagnostic_tools.append(mcp_tools[tool_name])

            if not diagnostic_tools:
                print("❌ 没有可用的诊断工具")
                return

            print(f"🔍 加载了 {len(diagnostic_tools)} 个诊断工具")

            # 创建诊断专家
            k8s_diagnoser = Agent(
                **agents_config['k8s_diagnoser'],
                tools=diagnostic_tools,
                llm=qwen_llm,
                verbose=False,
            )

            # 创建诊断任务
            test_task = Task(
                **tasks_config['k8s_diagnostic_test_task'],
                agent=k8s_diagnoser
            )

            # 执行诊断
            crew = Crew(
                agents=[k8s_diagnoser],
                tasks=[test_task],
                process=Process.sequential,
                verbose=False
            )

            inputs = get_inputs()
            print(f"\n📋 开始诊断: {inputs['question']}")
            print("─" * 50)

            crew.kickoff(inputs=inputs)


if __name__ == "__main__":
    run_k8s_test()
