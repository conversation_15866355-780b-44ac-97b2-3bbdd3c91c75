import os
import yaml
import warnings
import json
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from crewai.utilities.events import (
    CrewKickoffCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    LLMStreamChunkEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener

# --- 屏蔽无关的 DeprecationWarning ---
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 加载环境变量
load_dotenv()

# --- 事件监听器定义 (V5 - Final) ---


class K8sTestStreamingListener(BaseEventListener):
    """
    监听 CrewAI 事件并提供简洁的、纯文本的流式输出。
    """

    def __init__(self):
        super().__init__()
        self._thought_buffer = ""
        self._current_step = 0

    def setup_listeners(self, crewai_event_bus):
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_chunk(source, event):
            if hasattr(event, 'chunk') and isinstance(event.chunk, str):
                self._thought_buffer += event.chunk

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(source, event):
            self._current_step += 1

            # 显示思考过程
            if self._thought_buffer:
                thought_lines = self._thought_buffer.strip().split('\n')
                clean_thought = []
                for line in thought_lines:
                    if line.strip() and not line.startswith('Action:') and not line.startswith('Action Input:'):
                        clean_thought.append(line.strip())

                if clean_thought:
                    print(f"\n🤔 **步骤 {self._current_step} - 分析思路:**")
                    print("   " + "\n   ".join(clean_thought))

                self._thought_buffer = ""

            # 显示工具调用 - 修复属性名
            tool_input = getattr(event, 'input_args',
                                 getattr(event, 'tool_input', {}))
            print(f"\n🛠️  **执行操作:** `{event.tool_name}`")
            if tool_input:
                formatted_input = json.dumps(
                    tool_input, indent=2, ensure_ascii=False)
                print(f"   参数: {formatted_input}")

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(source, event):
            print(f"\n✅ **执行结果:**")
            print("```")
            print(event.output)
            print("```")

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(source, event):
            # 处理最终思考
            if self._thought_buffer:
                final_lines = self._thought_buffer.strip().split('\n')
                clean_final = []
                for line in final_lines:
                    if line.strip() and not line.startswith('Final Answer:'):
                        clean_final.append(line.strip())

                if clean_final:
                    print(f"\n🎯 **最终分析:**")
                    print("   " + "\n   ".join(clean_final))

            print(f"\n{'='*60}")
            print("🎉 **诊断完成 - 最终报告**")
            print(f"{'='*60}")
            print(event.output)


qwen_llm = LLM(
    model="openai/qwen-turbo-latest",
    base_url=os.getenv("QWEN_BASE_URL"),
    api_key=os.getenv("QWEN_API_KEY"),
    temperature=0.3,
    # 启用流式输出以配合事件监听器
    stream=True,
)


def load_yaml(path: str) -> dict:
    """加载并解析 YAML 文件"""
    with open(path, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)


def get_inputs() -> dict:
    """获取用户输入或从环境变量加载"""
    namespace = os.getenv("TEST_NAMESPACE", "default")
    pod_name = os.getenv("TEST_POD_NAME", "")
    question = (
        f"请在命名空间 '{namespace}' 中检查所有 Pod 的状态。"
        if not pod_name
        else f"请在命名空间 '{namespace}' 中详细描述 Pod '{pod_name}' 的状态。"
    )

    inputs = {
        "namespace": namespace,
        "pod_name": pod_name,
        "question": question,
    }
    return inputs


def run_k8s_test():
    """运行 Kubernetes 诊断专家测试"""
    print("🚀 开始运行 Kubernetes 诊断专家测试...")

    # 实例化新的监听器
    K8sTestStreamingListener()

    # MCP 服务器配置 (Kubernetes 工具服务器)
    mcp_server_config = {
        "url": "http://localhost:3000/sse",
        "transport": "sse"
    }

    # 加载 Agent 和 Task 配置
    try:
        agents_config = load_yaml('config/agents.yaml')
        tasks_config = load_yaml('config/tasks.yaml')
    except FileNotFoundError as e:
        print(f"❌ 错误：配置文件未找到 - {e}")
        print("请确保 'config/agents.yaml' 和 'config/tasks.yaml' 文件存在于测试目录中。")
        return

    # 使用 MCP Server Adapter 连接并获取工具
    with MCPServerAdapter(mcp_server_config) as mcp_tools:
        print("✅ 成功连接到 MCP 服务器。")

        available_tools = [tool.name for tool in mcp_tools]
        print(f"🛠️ 可用工具: {available_tools}")

        # 检查所需工具是否可用
        required_tools = ["kubectl_get", "kubectl_describe", "kubectl_logs"]
        if not all(tool in available_tools for tool in required_tools):
            print("❌ 错误：一个或多个必需的 Kubernetes 工具在 MCP 服务器上不可用。")
            print(f"需要: {required_tools}")
            return

        # 创建 k8s_diagnoser Agent
        k8s_diagnoser_config = agents_config['k8s_diagnoser']
        k8s_diagnoser = Agent(
            **k8s_diagnoser_config,
            tools=[mcp_tools["kubectl_get"],
                   mcp_tools["kubectl_describe"], mcp_tools["kubectl_logs"]],
            llm=qwen_llm,
            verbose=False,  # 由监听器接管输出
        )

        # 创建测试任务
        test_task_config = tasks_config['k8s_diagnostic_test_task']
        test_task = Task(
            **test_task_config,
            agent=k8s_diagnoser
        )

        # 组建并启动 Crew
        crew = Crew(
            agents=[k8s_diagnoser],
            tasks=[test_task],
            process=Process.sequential,
            verbose=False  # 由监听器接管输出
        )

        # 获取输入并启动任务
        inputs = get_inputs()
        print("\n📋 开始执行诊断任务...")
        print(f"   - 问题: {inputs['question']}")

        crew.kickoff(inputs=inputs)


if __name__ == "__main__":
    run_k8s_test()
