import os
import yaml
import warnings
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from crewai.utilities.events import (
    CrewKickoffCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    LLMStreamChunkEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener

warnings.filterwarnings("ignore")
load_dotenv()


class K8sTestStreamingListener(BaseEventListener):
    """清洁的 ReAct 流程监听器"""

    def __init__(self):
        super().__init__()
        self._current_step = 0
        self._current_thought = ""
        self._in_thought = False

    def setup_listeners(self, crewai_event_bus):
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_chunk(_, event):
            if hasattr(event, 'chunk') and isinstance(event.chunk, str):
                chunk = event.chunk

                # 检测思考开始
                if 'Thought:' in chunk:
                    self._in_thought = True
                    self._current_thought = chunk.split('Thought:')[-1].strip()
                elif self._in_thought:
                    # 检测思考结束（遇到 Action: 或 Final Answer:）
                    if 'Action:' in chunk or 'Final Answer:' in chunk:
                        self._in_thought = False
                    else:
                        self._current_thought += chunk

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(_, event):
            self._current_step += 1

            # 显示思考内容
            if self._current_thought.strip():
                clean_thought = self._current_thought.strip().replace('\n', ' ')
                if len(clean_thought) > 15:
                    print(f"\n🧠 **步骤 {self._current_step}**")
                    print(
                        f"   💭 思考: {clean_thought[:100]}{'...' if len(clean_thought) > 100 else ''}")

            # 显示工具执行
            tool_name = getattr(event, 'tool_name', 'unknown_tool')
            print(f"   🔧 执行: {tool_name}")

            # 重置思考缓冲
            self._current_thought = ""

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(_, event):
            # 简洁的观察结果
            if hasattr(event, 'output'):
                output = str(event.output)
                if any(keyword in output for keyword in ['Failed', 'Error', 'OOM', 'CrashLoop']):
                    print(f"   👁️ 观察: 发现异常")
                elif len(output) > 50:
                    print(f"   👁️ 观察: 获取数据")
                else:
                    print(f"   👁️ 观察: 完成")

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(_, event):
            print(f"\n{'='*50}")
            print(f"🎯 **SMART 诊断结果**")
            print(f"{'='*50}")
            print(event.output)
            print(f"{'='*50}")


qwen_llm = LLM(model="openai/qwen-turbo-latest", base_url=os.getenv("QWEN_BASE_URL"),
               api_key=os.getenv("QWEN_API_KEY"), temperature=0.3, stream=True)


def load_yaml(path: str) -> dict:
    with open(path, 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)


def get_inputs() -> dict:
    namespace = os.getenv("TEST_NAMESPACE", "default")
    pod_name = os.getenv("TEST_POD_NAME", "")
    question = f"请在命名空间 '{namespace}' 中检查所有 Pod 的状态。" if not pod_name else f"请在命名空间 '{namespace}' 中详细描述 Pod '{pod_name}' 的状态。"
    return {"namespace": namespace, "pod_name": pod_name, "question": question}


def run_k8s_test():
    print("🚀 开始运行 Kubernetes 诊断专家测试...")
    K8sTestStreamingListener()
    mcp_server_config = {
        "url": "http://localhost:3000/sse", "transport": "sse"}

    # 加载配置并连接 MCP 服务器
    agents_config = load_yaml('config/agents.yaml')
    tasks_config = load_yaml('config/tasks.yaml')
    if not agents_config or not tasks_config:
        print("❌ 配置文件加载失败")
        return

    with MCPServerAdapter(mcp_server_config) as mcp_tools:
        print("✅ 连接到 MCP 服务器")

        available_tools = [tool.name for tool in mcp_tools]

        # 收集诊断工具
        diagnostic_tool_names = ["kubectl_get", "kubectl_describe", "kubectl_logs",
                                 "explain_resource", "kubectl_context", "list_api_resources", "ping"]
        diagnostic_tools = [mcp_tools[name]
                            for name in diagnostic_tool_names if name in available_tools]

        if not diagnostic_tools:
            print("❌ 没有可用的诊断工具")
            return
        print(f"🔍 加载了 {len(diagnostic_tools)} 个诊断工具")

        # 创建并执行诊断
        k8s_diagnoser = Agent(
            **agents_config['k8s_diagnoser'], tools=diagnostic_tools, llm=qwen_llm, verbose=False)
        test_task = Task(
            **tasks_config['k8s_diagnostic_test_task'], agent=k8s_diagnoser)
        crew = Crew(agents=[k8s_diagnoser], tasks=[test_task],
                    process=Process.sequential, verbose=False)

        inputs = get_inputs()
        print(f"\n📋 开始诊断: {inputs['question']}")
        print("─" * 50)
        crew.kickoff(inputs=inputs)


if __name__ == "__main__":
    run_k8s_test()
