#!/usr/bin/env python3
"""
调试工具加载问题的脚本
"""

import os
import warnings
from dotenv import load_dotenv
from crewai_tools import MCPServerAdapter

# 屏蔽警告
warnings.filterwarnings("ignore")

def debug_mcp_tools():
    """调试 MCP 工具加载"""
    print("🔧 开始调试 MCP 工具加载...")
    
    # 加载环境变量
    load_dotenv()
    
    # MCP 服务器配置
    mcp_config = {
        "url": "http://localhost:3000/sse",
        "transport": "sse"
    }
    
    try:
        with MCPServerAdapter(mcp_config) as mcp_tools:
            print("✅ 成功连接到 MCP 服务器")
            
            # 获取所有可用工具
            available_tools = [tool.name for tool in mcp_tools]
            print(f"📋 所有可用工具 ({len(available_tools)} 个):")
            for i, tool in enumerate(available_tools, 1):
                print(f"  {i:2d}. {tool}")
            
            # 测试诊断工具
            diagnostic_tool_names = [
                "kubectl_get",
                "kubectl_describe", 
                "kubectl_logs",
                "explain_resource",
                "kubectl_context",
                "list_api_resources",
                "ping"
            ]
            
            print(f"\n🔍 测试诊断工具加载:")
            successful_tools = []
            
            for tool_name in diagnostic_tool_names:
                if tool_name in available_tools:
                    try:
                        tool_obj = mcp_tools[tool_name]
                        successful_tools.append(tool_name)
                        print(f"  ✅ {tool_name} - 加载成功")
                        print(f"     类型: {type(tool_obj)}")
                        print(f"     名称: {tool_obj.name if hasattr(tool_obj, 'name') else 'N/A'}")
                    except Exception as e:
                        print(f"  ❌ {tool_name} - 加载失败: {e}")
                else:
                    print(f"  ⚠️  {tool_name} - 不在可用工具列表中")
            
            print(f"\n📊 总结:")
            print(f"  - 可用工具总数: {len(available_tools)}")
            print(f"  - 成功加载的诊断工具: {len(successful_tools)}")
            print(f"  - 诊断工具列表: {successful_tools}")
            
            if len(successful_tools) == 0:
                print(f"\n❌ 严重问题：没有任何诊断工具可用！")
                print(f"这解释了为什么 AI 会生成虚假报告。")
            elif len(successful_tools) < 3:
                print(f"\n⚠️  警告：核心诊断工具不完整")
                print(f"缺少的核心工具可能影响诊断质量。")
            else:
                print(f"\n✅ 诊断工具加载正常")
            
            # 测试一个简单的工具调用
            if "kubectl_get" in successful_tools:
                print(f"\n🧪 测试 kubectl_get 工具调用:")
                try:
                    # 这里不实际调用，只是验证工具对象
                    kubectl_get_tool = mcp_tools["kubectl_get"]
                    print(f"  ✅ kubectl_get 工具对象获取成功")
                    print(f"  📝 工具描述: {getattr(kubectl_get_tool, 'description', 'N/A')}")
                except Exception as e:
                    print(f"  ❌ kubectl_get 工具测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 连接 MCP 服务器失败: {e}")
        print(f"请确保:")
        print(f"  1. MCP 服务器正在运行 (http://localhost:3000)")
        print(f"  2. 网络连接正常")
        print(f"  3. 环境变量配置正确")

if __name__ == "__main__":
    debug_mcp_tools()
