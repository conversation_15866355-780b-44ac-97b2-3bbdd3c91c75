#!/usr/bin/env python3
"""
Kubernetes 诊断专家 - 清洁版测试脚本
专注解决输出截断和显示问题
"""

import os
import yaml
import warnings
import sys
from datetime import datetime
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from crewai.utilities.events import (
    CrewKickoffCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    LLMStreamChunkEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener

# 屏蔽警告
warnings.filterwarnings("ignore")


class CleanK8sListener(BaseEventListener):
    """清洁的事件监听器，解决输出截断问题"""

    def __init__(self):
        super().__init__()
        self._thought_buffer = ""
        self._current_step = 0
        self._start_time = datetime.now()

    def setup_listeners(self, crewai_event_bus):
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_chunk(_, event):
            if hasattr(event, 'chunk') and isinstance(event.chunk, str):
                self._thought_buffer += event.chunk

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(_, event):
            try:
                self._current_step += 1
                
                # 显示完整的思考过程（不过滤）
                if self._thought_buffer:
                    print(f"\n🧠 **步骤 {self._current_step} - 完整思考过程:**")
                    print("─" * 60)
                    print(self._thought_buffer.strip())
                    print("─" * 60)
                    self._thought_buffer = ""

                # 显示工具调用
                tool_name = getattr(event, 'tool_name', 'unknown_tool')
                print(f"\n🔧 **执行工具:** `{tool_name}`")
                
            except Exception as e:
                print(f"❌ 事件处理错误: {e}")

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(_, event):
            try:
                print(f"✅ **工具执行完成**")
                # 显示工具输出的前500字符
                if hasattr(event, 'output'):
                    output = str(event.output)
                    if len(output) > 500:
                        print(f"📄 **工具输出预览:** {output[:500]}...")
                    else:
                        print(f"📄 **工具输出:** {output}")
            except Exception as e:
                print(f"❌ 输出处理错误: {e}")

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(_, event):
            elapsed = datetime.now() - self._start_time
            print(f"\n{'='*80}")
            print(f"🎉 **Kubernetes 诊断完成** (耗时: {elapsed.total_seconds():.1f}秒)")
            print(f"{'='*80}")
            print(event.output)
            print(f"{'='*80}")


def load_yaml_safe(path: str) -> dict:
    """安全加载 YAML 文件"""
    try:
        with open(path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    except Exception as e:
        print(f"❌ 加载配置文件失败: {path} - {e}")
        return {}


def create_llm():
    """创建 LLM 配置"""
    return LLM(
        model="openai/qwen-turbo-latest",
        base_url=os.getenv("QWEN_BASE_URL"),
        api_key=os.getenv("QWEN_API_KEY"),
        temperature=0.2,
        stream=True,
    )


def run_clean_k8s_test():
    """运行清洁版 Kubernetes 诊断测试"""
    print("🚀 Kubernetes 诊断专家 - 清洁版")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 初始化监听器
    CleanK8sListener()
    
    # MCP 服务器配置
    mcp_config = {
        "url": "http://localhost:3000/sse",
        "transport": "sse"
    }
    
    # 加载配置
    agents_config = load_yaml_safe('config/agents.yaml')
    tasks_config = load_yaml_safe('config/tasks.yaml')
    
    if not agents_config or not tasks_config:
        print("❌ 配置文件加载失败")
        return
    
    try:
        with MCPServerAdapter(mcp_config) as mcp_tools:
            print("✅ 已连接到 MCP 服务器")
            
            available_tools = [tool.name for tool in mcp_tools]
            
            # 诊断工具列表
            diagnostic_tool_names = [
                "kubectl_get", "kubectl_describe", "kubectl_logs",
                "explain_resource", "kubectl_context", "list_api_resources", "ping"
            ]
            
            # 收集诊断工具
            diagnostic_tools = []
            for tool_name in diagnostic_tool_names:
                if tool_name in available_tools:
                    try:
                        diagnostic_tools.append(mcp_tools[tool_name])
                        print(f"✅ 加载工具: {tool_name}")
                    except Exception as e:
                        print(f"❌ 工具加载失败 {tool_name}: {e}")
            
            if not diagnostic_tools:
                print("❌ 没有可用的诊断工具")
                return
            
            print(f"🔍 共加载 {len(diagnostic_tools)} 个诊断工具")
            
            # 创建诊断专家
            llm = create_llm()
            k8s_expert = Agent(
                **agents_config['k8s_diagnoser'],
                tools=diagnostic_tools,
                llm=llm,
                verbose=False,
            )
            
            # 创建诊断任务
            diagnostic_task = Task(
                **tasks_config['k8s_diagnostic_test_task'],
                agent=k8s_expert
            )
            
            # 创建诊断团队
            diagnostic_crew = Crew(
                agents=[k8s_expert],
                tasks=[diagnostic_task],
                process=Process.sequential,
                verbose=False
            )
            
            # 测试输入
            inputs = {
                "namespace": "default",
                "pod_name": "oom-pod",
                "question": "请详细诊断 Pod 'oom-pod' 的问题，并提供解决方案。"
            }
            
            print(f"\n🎯 开始诊断任务:")
            print(f"📍 目标: {inputs['question']}")
            print(f"🔍 命名空间: {inputs['namespace']}")
            print(f"🎯 目标 Pod: {inputs['pod_name']}")
            
            print("\n" + "─" * 80)
            
            # 执行诊断
            diagnostic_crew.kickoff(inputs=inputs)
            
    except Exception as e:
        print(f"❌ 执行错误: {e}")


if __name__ == "__main__":
    run_clean_k8s_test()
