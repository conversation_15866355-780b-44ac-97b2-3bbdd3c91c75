# 修改 `run_test.py` 以优化流式输出的计划

为了解决 `Thought` 格式问题并提供更简洁、核心的输出，我们将对 `run_test.py` 文件进行以下修改。所有新的代码都将直接添加到此文件中，以符合当前模式的限制。

## 1. 引入必要的事件和模块

在文件的开头，我们将从 `crewai.utilities.events` 导入所有需要的事件类，以及 `BaseEventListener`。

```python
from crewai.utilities.events import (
    CrewKickoffStartedEvent,
    CrewKickoffCompletedEvent,
    AgentExecutionCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    LLMStreamChunkEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener
```

## 2. 在 `run_test.py` 中定义事件监听器类

我们将直接在 `run_test.py` 文件中定义一个新的监听器类 `K8sTestStreamingListener`。这个类将负责捕获和格式化输出。

```python
class K8sTestStreamingListener(BaseEventListener):
    """
    此类用于监听 CrewAI 事件并提供定制化的、简洁的流式输出。
    它只打印核心信息：思考过程、工具调用与输出、最终结果。
    """
    def __init__(self):
        super().__init__()
        self._thought_buffer = ""

    def setup_listeners(self, crewai_event_bus):
        # 监听 LLM 数据块，聚合思考过程
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_chunk(source, event):
            if 'thought' in event.data:
                self._thought_buffer += event.data['thought']
            if 'tool_input' in event.data and self._thought_buffer:
                print(f"\n🤔 **思考过程:**\n{self._thought_buffer.strip()}")
                self._thought_buffer = "" # 清空缓冲区

        # 监听工具开始使用
        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(source, event):
            print(f"🛠️  **调用工具:** `{event.tool_name}`")
            print(f"   - **输入:** {event.input_params}")

        # 监听工具结束使用
        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(source, event):
            print(f"✅ **工具输出:**\n---\n{event.output}\n---")

        # 监听整个 Crew 执行完毕
        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(source, event):
            print("\n🎉 **诊断任务完成!**")
            print(f"\n**最终诊断结果:**\n---\n{event.output}\n---")
```

## 3. 修改 `run_k8s_test()` 函数

最后，我们将调整 `run_k8s_test` 函数来集成这个新的监听器。

- **实例化监听器**：在函数开头创建监听器实例。
- **移除 `step_callback`**：Agent 的 `step_callback` 将被移除，因为它的功能已被我们的监听器取代。
- **设置 `verbose=False`**：Crew 的 `verbose` 标志将设为 `False`，以避免默认的冗长输出。

```python
def run_k8s_test():
    """运行 Kubernetes 诊断专家测试"""
    print("🚀 开始运行 Kubernetes 诊断专家测试...")

    # 实例化新的监听器
    listener = K8sTestStreamingListener()

    # MCP 服务器配置... (代码无变化)
    
    # 加载 Agent 和 Task 配置... (代码无变化)

    # 使用 MCP Server Adapter 连接并获取工具
    with MCPServerAdapter(mcp_server_config) as mcp_tools:
        # ... (内部逻辑无变化)

        # 创建 k8s_diagnoser Agent (移除 step_callback)
        k8s_diagnoser_config = agents_config['k8s_diagnoser']
        k8s_diagnoser = Agent(
            **k8s_diagnoser_config,
            tools=[mcp_tools["kubectl_get"],
                   mcp_tools["kubectl_describe"], mcp_tools["kubectl_logs"]],
            llm=qwen_llm,
            verbose=False,  # 由监听器接管输出
        )

        # 创建测试任务... (代码无变化)

        # 组建并启动 Crew (设置 verbose=False)
        crew = Crew(
            agents=[k8s_diagnoser],
            tasks=[test_task],
            process=Process.sequential,
            verbose=False # 由监听器接管输出
        )

        # 获取输入并启动任务
        inputs = get_inputs()
        print("\n📋 开始执行诊断任务...")
        print(f"   - 问题: {inputs['question']}")

        result = crew.kickoff(inputs=inputs)

        # 移除旧的打印结果部分，因为监听器会处理
        # print("\n\n✅ 测试完成！")
        # ...
```

## 计划审阅

请您审阅以上计划。如果计划清晰且符合您的预期，我将请求切换到 **Code** 模式以应用这些更改。