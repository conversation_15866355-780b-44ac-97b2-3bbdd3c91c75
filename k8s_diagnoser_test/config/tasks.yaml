# ===================================================================
#  Kubernetes 诊断专家 - 增强任务配置
# ===================================================================

k8s_diagnostic_test_task:
  description: >
    根据问题 '{question}' 对 Kubernetes 集群进行诊断。

    **执行步骤：**
    1. 理解问题意图，判断是需要全局扫描还是针对特定 Pod 进行深度分析
    2. 使用 'kubectl_get' 来获取 Pod 列表和它们的状态
    3. 如果问题涉及特定的 Pod（在 '{pod_name}' 中指定），请使用
       'kubectl_describe' 来获取该 Pod 的详细事件、配置和状态
    4. 如果需要查看日志，可以使用 'kubectl_logs'
    5. 综合所有信息，形成一个清晰的诊断摘要

  expected_output: >
    **K8s 诊断报告**

    **状态概览:** [正常Pod数/异常Pod数]
    **问题Pod:** [名称] - [状态] - [根因]
    **资源配置:** [requests/limits关键信息]
    **修复方案:** [具体步骤，1-3条]
    **监控建议:** [关键指标]

    要求：高信息密度，重点突出，避免冗余描述。
