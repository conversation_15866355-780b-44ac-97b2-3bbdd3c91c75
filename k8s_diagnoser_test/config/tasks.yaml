# ===================================================================
#  Kubernetes 诊断专家 - 增强任务配置
# ===================================================================

k8s_diagnostic_test_task:
  description: >
    执行 Kubernetes 集群智能诊断任务：'{question}'

    **任务目标：**
    运用 SMART 诊断方法论，对指定的 Kubernetes 资源进行全面健康检查和问题诊断。

    **执行步骤：**

    🔍 **第一阶段：智能扫描 (Scan)**
    1. 分析问题意图，确定诊断范围（全局 vs 特定资源）
    2. 使用 `kubectl_get` 获取目标命名空间的资源状态概览
    3. 自动识别异常状态：Failed, CrashLoopBackOff, Pending, OOMKilled, Evicted
    4. 如果指定了特定 Pod '{pod_name}'，重点关注该资源

    📊 **第二阶段：深度监控 (Monitor)**
    1. 对发现的异常资源使用 `kubectl_describe` 获取详细信息
    2. 重点检查：Events、Resource limits/requests、Container status
    3. 必要时使用 `kubectl_logs` 查看容器日志
    4. 使用 `explain_resource` 解释复杂的资源配置

    🎯 **第三阶段：根因分析 (Analyze)**
    1. 识别问题类型：资源不足、配置错误、应用故障、调度问题
    2. 分析资源配置合理性（requests vs limits vs 实际使用）
    3. 检查依赖关系和关联影响
    4. 评估问题严重程度和影响范围

    💡 **第四阶段：解决建议 (Recommend)**
    1. 提供具体的修复步骤和配置调整建议
    2. 给出资源优化建议（CPU/内存配置）
    3. 推荐监控和预防措施
    4. 提供相关的 kubectl 命令示例

    🚀 **第五阶段：总结跟踪 (Track)**
    1. 汇总关键发现和行动项
    2. 按优先级排序问题
    3. 提供后续监控建议

  expected_output: >
    **Kubernetes 集群诊断报告**

    📋 **执行摘要**
    - 诊断范围：[命名空间/资源范围]
    - 检查时间：[时间戳]
    - 总体健康状态：[健康/警告/严重]
    - 发现问题数量：[数量统计]

    🔍 **详细发现**

    **正常资源：**
    - 列出运行正常的 Pod 及其状态
    - 资源使用情况概览

    **异常资源：**
    对每个异常资源提供：
    - 资源名称和当前状态
    - 问题类型和严重程度 (🔴高危 🟡中等 🟢轻微)
    - 根因分析
    - 相关事件和错误信息
    - 资源配置详情 (requests/limits)

    💡 **解决方案**

    **立即行动项：**
    - 紧急修复步骤（带具体命令）
    - 配置调整建议

    **优化建议：**
    - 资源配置优化
    - 监控改进建议
    - 预防措施

    **后续跟踪：**
    - 需要持续监控的指标
    - 建议的检查频率
    - 相关文档和最佳实践链接

    ---
    **注意：** 所有建议都应该包含具体的 kubectl 命令示例和配置文件片段。
