# ===================================================================
#  Kubernetes 诊断专家 - 增强任务配置
# ===================================================================

k8s_diagnostic_test_task:
  description: >
    根据问题 '{question}' 对 Kubernetes 集群进行诊断。

    **执行步骤：**
    1. 理解问题意图，判断是需要全局扫描还是针对特定 Pod 进行深度分析
    2. 使用 'kubectl_get' 来获取 Pod 列表和它们的状态
    3. 如果问题涉及特定的 Pod（在 '{pod_name}' 中指定），请使用
       'kubectl_describe' 来获取该 Pod 的详细事件、配置和状态
    4. 如果需要查看日志，可以使用 'kubectl_logs'
    5. 综合所有信息，形成一个清晰的诊断摘要

  expected_output: >
    一份简洁明了的诊断报告，回答初始问题 '{question}'。
    报告应包含：
    - 所检查的 Pod(s) 的状态摘要
    - 如果是针对特定 Pod，则提供其详细的资源配置、最近的事件和状态
    - 对发现的任何潜在问题（如 CrashLoopBackOff, Evicted, OOMKilled）的明确指出
    - 问题根因分析和具体的修复建议
