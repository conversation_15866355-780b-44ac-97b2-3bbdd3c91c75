# Kubernetes 诊断专家 (k8s_diagnoser) - 独立测试环境

本项目旨在从一个大型的 Multi-Agent 系统中，提取出 "Kubernetes 诊断专家" (`k8s_diagnoser`) 的核心功能，进行独立的测试和验证。

这使得我们可以在一个隔离、轻量级的环境中，专注于测试 Agent 的工具调用能力（通过 MCP 连接到外部 Kubernetes 工具服务）和任务执行逻辑，而无需运行整个复杂的 Agent 系统。

## 📝 系统要求

在运行此测试之前，请确保满足以下先决条件：

1.  **Python 3.8+**: 确保您的环境中已安装 Python。
2.  **Kubernetes 工具 MCP 服务器**: **这是最关键的一步**。本项目中的 Agent 通过 MCP (Model Context Protocol) 调用一个外部服务来执行 `kubectl` 命令。您必须确保这个工具服务正在本地运行，并且监听在 `http://localhost:3000`。
3.  **Azure OpenAI 账户**: 测试脚本默认配置为使用 Azure OpenAI。您需要拥有一个可用的 Azure OpenAI 服务实例和相应的凭据。

## 🚀 快速开始

请按照以下步骤设置并运行测试：

### 1. 创建并激活虚拟环境

为了保持依赖隔离，强烈建议使用虚拟环境。

```bash
# 进入测试项目目录
cd k8s_diagnoser_test

# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境 (macOS/Linux)
source venv/bin/activate

# 激活虚拟环境 (Windows)
# venv\Scripts\activate
```

### 2. 安装依赖

在激活的虚拟环境中，使用 `pip` 安装所有必需的库。

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

本项目使用 `.env` 文件来管理敏感信息和配置。

```bash
# 1. 从模板复制 .env 文件
cp .env.example .env

# 2. 编辑 .env 文件
#    使用您喜欢的文本编辑器打开 .env 文件，并填入您的 Azure OpenAI 凭据。
#    您也可以按需修改 TEST_NAMESPACE 和 TEST_POD_NAME。
#
#    nano .env
```

### 4. 运行测试

一切准备就绪后，执行以下命令来启动测试脚本：

```bash
python run_test.py
```

您将在终端看到详细的输出，包括与 MCP 服务器的连接状态、Agent 的思考过程以及最终的诊断结果。

## 📂 文件结构

```
k8s_diagnoser_test/
├── config/
│   ├── agents.yaml       # 仅包含 k8s_diagnoser 的定义
│   └── tasks.yaml        # 包含一个用于测试的 k8s 诊断任务
├── .env.example          # 环境变量模板
├── README.md             # 本说明文件
├── requirements.txt      # Python 依赖列表
└── run_test.py           # 核心测试脚本
```

---
Happy Testing!