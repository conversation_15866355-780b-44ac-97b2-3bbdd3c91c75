#!/usr/bin/env python3
"""
Kubernetes 诊断专家 - 增强版测试脚本
优化了用户体验和诊断能力
"""

import os
import yaml
import warnings
import sys
from datetime import datetime
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter
from crewai.utilities.events import (
    CrewKickoffCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    LLMStreamChunkEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener

# 完全屏蔽警告
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'


class EnhancedK8sListener(BaseEventListener):
    """增强的事件监听器，提供更好的用户体验"""

    def __init__(self):
        super().__init__()
        self._thought_buffer = ""
        self._current_step = 0
        self._start_time = datetime.now()

    def setup_listeners(self, crewai_event_bus):
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_chunk(_, event):
            if hasattr(event, 'chunk') and isinstance(event.chunk, str):
                self._thought_buffer += event.chunk

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(_, event):
            try:
                self._current_step += 1

                # 显示思考过程
                if self._thought_buffer:
                    thought_lines = self._thought_buffer.strip().split('\n')
                    clean_thought = []
                    for line in thought_lines:
                        line = line.strip()
                        if (line and
                            not line.startswith('Action:') and
                            not line.startswith('Action Input:') and
                                not line.startswith('Thought:')):
                            clean_thought.append(line)

                    if clean_thought:
                        print(f"\n🧠 **步骤 {self._current_step} - AI 分析思路:**")
                        for thought in clean_thought[:3]:  # 只显示前3行关键思路
                            print(f"   💭 {thought}")
                        if len(clean_thought) > 3:
                            print(f"   ... (共 {len(clean_thought)} 条分析)")

                    self._thought_buffer = ""

                # 显示工具调用
                tool_name = getattr(event, 'tool_name', 'unknown_tool')
                print(f"\n🔧 **执行工具:** `{tool_name}`")

            except Exception:
                pass

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(_, event):
            try:
                print(f"✅ **工具执行完成**")
                # 不显示详细输出，避免信息过载
            except Exception:
                pass

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(_, event):
            elapsed = datetime.now() - self._start_time
            print(f"\n{'='*80}")
            print(
                f"🎉 **Kubernetes 诊断完成** (耗时: {elapsed.total_seconds():.1f}秒)")
            print(f"{'='*80}")
            print(event.output)
            print(f"{'='*80}")


def load_yaml_safe(path: str) -> dict:
    """安全加载 YAML 文件"""
    try:
        with open(path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    except Exception as e:
        print(f"❌ 加载配置文件失败: {path} - {e}")
        return {}


def get_test_inputs() -> dict:
    """获取测试输入参数"""
    # 支持多种测试场景
    test_scenarios = {
        "1": {
            "namespace": "default",
            "pod_name": "",
            "question": "请对命名空间 'default' 进行全面的健康检查，识别所有潜在问题。"
        },
        "2": {
            "namespace": "default",
            "pod_name": "oom-pod",
            "question": "请详细诊断 Pod 'oom-pod' 的问题，并提供解决方案。"
        },
        "3": {
            "namespace": "kube-system",
            "pod_name": "",
            "question": "检查系统命名空间 'kube-system' 的关键组件状态。"
        }
    }

    print("\n📋 选择诊断场景:")
    for key, scenario in test_scenarios.items():
        print(f"  {key}. {scenario['question']}")

    choice = input("\n请选择场景 (1-3, 默认1): ").strip() or "1"

    if choice in test_scenarios:
        return test_scenarios[choice]
    else:
        return test_scenarios["1"]


def create_enhanced_llm():
    """创建增强的 LLM 配置"""
    return LLM(
        model="openai/qwen-turbo-latest",
        base_url=os.getenv("QWEN_BASE_URL"),
        api_key=os.getenv("QWEN_API_KEY"),
        temperature=0.2,  # 降低温度，提高一致性
        stream=True,
    )


def run_enhanced_k8s_test():
    """运行增强版 Kubernetes 诊断测试"""
    print("🚀 Kubernetes 智能诊断专家 - 增强版")
    print("=" * 50)

    # 加载环境变量
    load_dotenv()

    # 初始化监听器
    EnhancedK8sListener()

    # MCP 服务器配置
    mcp_config = {
        "url": "http://localhost:3000/sse",
        "transport": "sse"
    }

    # 加载配置
    agents_config = load_yaml_safe('config/agents.yaml')
    tasks_config = load_yaml_safe('config/tasks.yaml')

    if not agents_config or not tasks_config:
        print("❌ 配置文件加载失败，请检查配置文件是否存在。")
        return

    try:
        # 连接 MCP 服务器
        with MCPServerAdapter(mcp_config) as mcp_tools:
            print("✅ 已连接到 Kubernetes MCP 服务器")

            available_tools = [tool.name for tool in mcp_tools]

            # 定义诊断专用工具（只读/分析工具）
            diagnostic_tool_names = [
                "kubectl_get",          # 获取资源状态
                "kubectl_describe",     # 获取详细信息和事件
                "kubectl_logs",         # 查看日志
                "explain_resource",     # 解释资源字段
                "kubectl_context",      # 查看当前上下文
                "list_api_resources",   # 列出可用资源类型
                "ping"                  # 连通性测试
            ]

            # 检查核心诊断工具
            core_tools = ["kubectl_get", "kubectl_describe", "kubectl_logs"]
            missing_tools = [
                tool for tool in core_tools if tool not in available_tools]

            if missing_tools:
                print(f"❌ 缺少核心诊断工具: {missing_tools}")
                print("无法继续执行诊断任务。")
                return

            # 收集可用的诊断工具（仅诊断相关）
            diagnostic_tools = []
            available_diagnostic_tools = []

            print(f"🔧 调试信息: 所有可用工具 = {available_tools}")
            print(
                f"🔧 调试信息: MCP工具对象 = {list(mcp_tools.keys()) if hasattr(mcp_tools, 'keys') else 'Not dict-like'}")

            for tool_name in diagnostic_tool_names:
                if tool_name in available_tools:
                    try:
                        tool_obj = mcp_tools[tool_name]
                        diagnostic_tools.append(tool_obj)
                        available_diagnostic_tools.append(tool_name)
                        print(f"✅ 成功加载诊断工具: {tool_name}")
                    except KeyError:
                        print(
                            f"❌ 工具 {tool_name} 在 available_tools 中但无法从 mcp_tools 获取")
                    except Exception as e:
                        print(f"❌ 加载工具 {tool_name} 时出错: {e}")
                else:
                    print(f"⚠️  诊断工具 {tool_name} 不在可用工具列表中")

            print(f"🔍 最终可用诊断工具: {available_diagnostic_tools}")

            if not diagnostic_tools:
                print("❌ 严重错误：没有任何诊断工具可用！")
                print("这将导致 AI 无法执行实际诊断，只能生成虚假报告。")
                return

            # 提醒用户关于修复工具的分离
            modification_tools = ["kubectl_apply", "kubectl_delete", "kubectl_create",
                                  "kubectl_scale", "kubectl_patch", "kubectl_rollout"]
            detected_mod_tools = [
                tool for tool in modification_tools if tool in available_tools]
            if detected_mod_tools:
                print(f"💡 提示: 检测到修复工具 {len(detected_mod_tools)} 个，诊断阶段不使用")

            # 创建增强的诊断专家
            llm = create_enhanced_llm()
            k8s_expert = Agent(
                **agents_config['k8s_diagnoser'],
                tools=diagnostic_tools,
                llm=llm,
                verbose=False,
            )

            # 创建诊断任务
            diagnostic_task = Task(
                **tasks_config['k8s_diagnostic_test_task'],
                agent=k8s_expert
            )

            # 创建诊断团队
            diagnostic_crew = Crew(
                agents=[k8s_expert],
                tasks=[diagnostic_task],
                process=Process.sequential,
                verbose=False
            )

            # 获取测试输入
            inputs = get_test_inputs()

            print(f"\n🎯 开始执行诊断任务...")
            print(f"📍 目标: {inputs['question']}")
            print(f"🔍 命名空间: {inputs['namespace']}")
            if inputs['pod_name']:
                print(f"🎯 目标 Pod: {inputs['pod_name']}")

            print("\n" + "─" * 60)

            # 执行诊断
            diagnostic_crew.kickoff(inputs=inputs)

    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        print("请确保:")
        print("  1. MCP 服务器正在运行 (http://localhost:3000)")
        print("  2. 环境变量配置正确")
        print("  3. 网络连接正常")


if __name__ == "__main__":
    run_enhanced_k8s_test()
