# AI-Ops SRE 团队（最终智能版）

本项目展示了一个面向生产的、复杂的 AI 代理团队，用于自动化 OOMKill 诊断。它通过利用 CrewAI 的高级特性，在灵活性与可控性之间取得平衡，模拟专家 SRE 的工作流程。

## 核心架构与工作流程

1.  **智能代理**：代理，尤其是 `k8s_diagnoser`，通过其在 `config/agents.yaml` 中的 `backstory` 获得明确的方法论。它们被引导有条理地工作：首先收集完整的目标列表，然后逐个处理目标，有效地形成内部“记忆”，避免重复操作。
2.  **灵活任务分配**：`config/tasks.yaml` 中的任务描述设计得非常灵活，指导代理关注*要实现什么*（如“排查 OOM 问题”），而不是死板地规定*如何做*（如“只调用一次工具”）。这使代理能根据实际情况自适应，比如只发现一个 OOMKilled pod 时只 describe 一个。
3.  **并行诊断与主管控制**：团队仍采用并行诊断阶段，随后由主管进行综合分析，确保高效与智能兼备。
4.  **`fastmcp` + `MCPServerAdapter`**：项目采用了正确且健壮的工具集成模式。

## 特性

- **有状态代理行为**：代理被引导采用“先收集再处理”的逻辑，既避免了失控循环，又保持了灵活性。
- **高级规划与协作**：充分利用 CrewAI 的最佳特性，实现可靠且智能的工作流。
- **并行任务执行**：诊断阶段最大化效率。
- **配置驱动**：所有代理与任务定义均在 `config/*.yaml` 文件中集中管理。

## 快速开始

### 1. 先决条件与安装

- Python 3.8+
- 来自阿里云（Dashscope）的 Qwen API Key
- `pip install -r requirements.txt`

### 2. 配置

将 `.env.example` 重命名为 `.env` 并填写你的 API Key。

### 3. 运行团队

执行主脚本。`MCPServerAdapter` 会自动管理 `fastmcp` 服务器的生命周期。
```bash
python crew.py
```

## 项目结构

- **`crew.py`**：主入口。编排规划、并行与串行任务工作流。
- **`mcp_server.py`**：定义 SRE 工具的 `fastmcp` 服务器。现已返回结构化数据（JSON），便于代理处理。
- **`config/`**：
    - **`agents.yaml`**：定义具备智能操作方法论的代理。
    - **`tasks.yaml`**：定义灵活且清晰的任务。
- **`requirements.txt` / `.env.example`**：项目初始化文件。