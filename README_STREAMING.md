# CrewAI 流式输出实现

本项目实现了CrewAI的流式输出功能，替代了传统的`verbose=True`方式，提供更好的实时用户体验。

## 🌟 主要特性

- **实时流式输出**: 利用LLM的流式响应能力，实时显示AI思考过程
- **事件驱动**: 基于CrewAI的事件监听系统，监听各种执行事件
- **可配置**: 支持自定义输出格式、过滤敏感信息等
- **更好的UX**: 提供清晰的进度指示和结构化输出

## 📁 文件结构

```
├── crew.py                        # 主要的Crew实现（已更新为流式输出）
├── streaming_listener.py          # 基础流式输出监听器
├── advanced_streaming_listener.py # 高级流式输出监听器
├── streaming_config.py            # 流式输出配置文件
├── streaming_example.py           # 使用示例
└── README_STREAMING.md            # 本文档
```

## 🚀 快速开始

### 1. 基础用法

```python
from crewai import Agent, Task, Crew, LLM
from advanced_streaming_listener import advanced_streaming_listener

# 配置LLM（关键：启用stream=True）
llm = LLM(
    model="openai/qwen-turbo-latest",
    base_url=os.getenv("QWEN_BASE_URL"),
    api_key=os.getenv("QWEN_API_KEY"),
    stream=True,  # 启用流式输出
)

# 创建代理（设置verbose=False）
agent = Agent(
    role="系统分析师",
    goal="分析系统问题",
    backstory="经验丰富的系统分析师",
    llm=llm,
    verbose=False  # 使用事件监听器而不是verbose
)

# 创建Crew（设置verbose=False）
crew = Crew(
    agents=[agent],
    tasks=[task],
    verbose=False  # 使用事件监听器而不是verbose
)

# 执行任务，将看到实时流式输出
result = crew.kickoff()
```

### 2. 配置自定义

在`streaming_config.py`中可以自定义：

```python
OUTPUT_CONFIG = {
    "show_timestamps": True,        # 显示时间戳
    "show_tool_args": True,         # 显示工具参数
    "max_tool_output_length": 300,  # 工具输出最大长度
    "sensitive_keywords": ["password", "token"],  # 敏感词过滤
}
```

## 🎯 核心原理

### 事件监听系统

CrewAI提供了丰富的事件系统，主要包括：

1. **LLM事件**:
   - `LLMCallStartedEvent`: LLM调用开始
   - `LLMStreamChunkEvent`: 流式输出块（关键事件）
   - `LLMCallCompletedEvent`: LLM调用完成

2. **Crew事件**:
   - `CrewKickoffStartedEvent`: Crew开始执行
   - `CrewKickoffCompletedEvent`: Crew执行完成

3. **Agent事件**:
   - `AgentExecutionStartedEvent`: Agent开始执行
   - `AgentExecutionCompletedEvent`: Agent执行完成

4. **Task事件**:
   - `TaskStartedEvent`: 任务开始
   - `TaskCompletedEvent`: 任务完成

5. **Tool事件**:
   - `ToolUsageStartedEvent`: 工具使用开始
   - `ToolUsageFinishedEvent`: 工具使用完成

### 流式输出实现

关键在于监听`LLMStreamChunkEvent`事件：

```python
@crewai_event_bus.on(LLMStreamChunkEvent)
def on_llm_stream_chunk(source, event):
    if hasattr(event, 'chunk') and event.chunk:
        print(event.chunk, end='', flush=True)
```

## 🔧 自定义监听器

创建自己的事件监听器：

```python
from crewai.utilities.events.base_event_listener import BaseEventListener
from crewai.utilities.events import LLMStreamChunkEvent

class MyCustomListener(BaseEventListener):
    def setup_listeners(self, crewai_event_bus):
        @crewai_event_bus.on(LLMStreamChunkEvent)
        def my_handler(source, event):
            # 自定义处理逻辑
            pass

# 创建实例以激活监听器
my_listener = MyCustomListener()
```

## 📊 输出示例

运行时你会看到类似这样的输出：

```
================================================================================
🚀 AI-Ops SRE 智能诊断团队启动
   开始时间: 2024-08-04 15:30:25
================================================================================

🤖 代理 [K8s诊断专家] 开始工作
------------------------------------------------------------

📋 任务: 排查Kubernetes集群中的OOM问题...

🧠 AI思考中...
💭 AI回应: 我将帮您分析Kubernetes集群中的OOM（Out of Memory）问题。让我从几个方面来进行诊断...

🔧 使用工具: kubectl_get
   参数: {'resource': 'pods', 'namespace': 'default'}
✅ 工具完成: kubectl_get
   结果: NAME                     READY   STATUS    RESTARTS   AGE...

✅ 代理 [K8s诊断专家] 工作完成
------------------------------------------------------------

================================================================================
✅ 智能诊断任务完成 (2m 34.5s)
   LLM调用次数: 5
   工具使用次数: 8
================================================================================

📊 最终诊断报告:
------------------------------------------------------------
经过详细分析，发现以下OOM问题和解决方案...
------------------------------------------------------------
```

## ⚙️ 配置选项

### OUTPUT_CONFIG

- `show_timestamps`: 是否显示时间戳
- `show_tool_args`: 是否显示工具参数
- `max_tool_output_length`: 工具输出最大长度
- `sensitive_keywords`: 需要过滤的敏感关键词
- `stream_buffer_size`: 流式输出缓冲区大小

### EVENT_FILTER

- `enabled_events`: 要监听的事件类型列表
- `ignored_events`: 要忽略的事件类型列表

### DISPLAY_FORMAT

- `separator_char`: 主分隔符字符
- `separator_length`: 主分隔符长度
- `icons`: 各种状态的图标配置

## 🎨 优势对比

| 特性 | verbose=True | 流式输出监听器 |
|------|-------------|---------------|
| 实时性 | ❌ 批量输出 | ✅ 实时流式 |
| 可定制 | ❌ 固定格式 | ✅ 完全可配置 |
| 用户体验 | ❌ 较差 | ✅ 优秀 |
| 性能影响 | ❌ 较大 | ✅ 较小 |
| 敏感信息过滤 | ❌ 无 | ✅ 支持 |
| 进度指示 | ❌ 无 | ✅ 详细 |

## 🛠️ 故障排除

### 1. 流式输出不工作

确保LLM配置中启用了流式输出：
```python
llm = LLM(
    model="your-model",
    stream=True,  # 必须设置
)
```

### 2. 没有看到事件输出

确保导入了监听器实例：
```python
from advanced_streaming_listener import advanced_streaming_listener
```

### 3. 输出格式不符合预期

检查`streaming_config.py`中的配置，或创建自定义监听器。

## 📝 注意事项

1. **导入监听器**: 必须导入监听器实例才能激活事件监听
2. **关闭verbose**: 设置`verbose=False`避免重复输出
3. **启用流式**: LLM必须配置`stream=True`
4. **性能考虑**: 频繁的输出操作可能影响性能
5. **线程安全**: 在多线程环境中使用时需要注意线程安全

## 🤝 贡献

欢迎提交改进建议和Bug报告！
