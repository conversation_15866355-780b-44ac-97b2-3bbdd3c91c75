"""
流式输出事件监听器
实现CrewAI的实时流式输出，替代verbose参数
"""
import sys
import time
from typing import Optional
from crewai.utilities.events import (
    CrewKickoffStartedEvent,
    CrewKickoffCompletedEvent,
    AgentExecutionStartedEvent,
    AgentExecutionCompletedEvent,
    TaskStartedEvent,
    TaskCompletedEvent,
    LLMStreamChunkEvent,
    LLMCallStartedEvent,
    LLMCallCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener


class StreamingOutputListener(BaseEventListener):
    """
    流式输出监听器
    监听CrewAI的各种事件，实现实时流式输出
    """

    def __init__(self):
        super().__init__()
        self.current_agent = None
        self.current_task = None
        self.streaming_buffer = ""
        self.start_time = None
        self.current_llm_context = None

    def _print_separator(self, char="=", length=80):
        """打印分隔符"""
        print(char * length)

    def _format_time_elapsed(self, start_time):
        """格式化时间差"""
        if start_time:
            elapsed = time.time() - start_time
            return f"({elapsed:.2f}s)"
        return ""

    def setup_listeners(self, crewai_event_bus):
        """设置事件监听器"""

        @crewai_event_bus.on(CrewKickoffStartedEvent)
        def on_crew_started(source, event):
            self.start_time = time.time()
            print("\n" + "="*80)
            print(f"🚀 AI-Ops SRE 团队开始执行任务")
            if hasattr(event, 'crew_name') and event.crew_name:
                print(f"   团队名称: {event.crew_name}")
            print("="*80)

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(source, event):
            elapsed = self._format_time_elapsed(self.start_time)
            print("\n" + "="*80)
            print(f"✅ 团队任务完成 {elapsed}")
            print("="*80)
            if hasattr(event, 'output') and event.output:
                print(f"\n📊 最终诊断报告:")
                print("-" * 60)
                print(f"{event.output}")
                print("-" * 60)

        @crewai_event_bus.on(AgentExecutionStartedEvent)
        def on_agent_started(source, event):
            if hasattr(event, 'agent') and event.agent:
                self.current_agent = event.agent.role
                print(f"\n🤖 代理 [{self.current_agent}] 开始工作")
                print("-" * 60)

        @crewai_event_bus.on(AgentExecutionCompletedEvent)
        def on_agent_completed(source, event):
            if hasattr(event, 'agent') and event.agent:
                print(f"\n✅ 代理 [{event.agent.role}] 任务完成")
                print("-" * 60)

        @crewai_event_bus.on(TaskStartedEvent)
        def on_task_started(source, event):
            if hasattr(event, 'task') and event.task:
                task_desc = event.task.description
                if len(task_desc) > 120:
                    task_desc = task_desc[:120] + "..."
                self.current_task = task_desc

                print(f"\n📋 任务开始: {task_desc}")
                if hasattr(event.task, 'expected_output'):
                    expected = event.task.expected_output
                    if len(expected) > 100:
                        expected = expected[:100] + "..."
                    print(f"   期望输出: {expected}")

        @crewai_event_bus.on(TaskCompletedEvent)
        def on_task_completed(source, event):
            print(f"\n✅ 任务完成")

        @crewai_event_bus.on(LLMCallStartedEvent)
        def on_llm_call_started(source, event):
            self.current_llm_context = "思考中"
            print(f"\n🧠 AI正在思考...")
            sys.stdout.flush()

        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_stream_chunk(source, event):
            """处理LLM流式输出块"""
            if hasattr(event, 'chunk') and event.chunk:
                # 如果是新的流式输出开始，先打印提示
                if not self.streaming_buffer:
                    print("💭 AI回应: ", end='', flush=True)

                # 直接输出流式内容
                chunk_text = str(event.chunk)
                print(chunk_text, end='', flush=True)
                self.streaming_buffer += chunk_text

        @crewai_event_bus.on(LLMCallCompletedEvent)
        def on_llm_call_completed(source, event):
            if self.streaming_buffer:
                print()  # 流式输出结束后换行
                self.streaming_buffer = ""
            self.current_llm_context = None
            sys.stdout.flush()

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(source, event):
            if hasattr(event, 'tool_name'):
                print(f"\n🔧 使用工具: {event.tool_name}")

                # 显示工具参数（如果存在且不敏感）
                if hasattr(event, 'input_args') and event.input_args:
                    # 过滤敏感信息
                    safe_args = {}
                    for key, value in event.input_args.items():
                        if key.lower() in ['password', 'token', 'key', 'secret']:
                            safe_args[key] = "***"
                        elif len(str(value)) > 200:
                            safe_args[key] = str(value)[:200] + "...[截断]"
                        else:
                            safe_args[key] = value
                    print(f"   参数: {safe_args}")

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(source, event):
            if hasattr(event, 'tool_name'):
                print(f"✅ 工具完成: {event.tool_name}")

                # 显示工具输出摘要
                if hasattr(event, 'output') and event.output:
                    output_str = str(event.output)
                    if len(output_str) > 300:
                        # 显示前150字符和后150字符
                        output_summary = (
                            output_str[:150] +
                            f"...[省略{len(output_str)-300}字符]..." +
                            output_str[-150:]
                        )
                    else:
                        output_summary = output_str
                    print(f"   结果: {output_summary}")


# 创建监听器实例
streaming_listener = StreamingOutputListener()
