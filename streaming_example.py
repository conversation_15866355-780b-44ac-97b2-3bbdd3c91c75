#!/usr/bin/env python3
"""
CrewAI 流式输出示例
演示如何使用事件监听器实现实时流式输出
"""

import os
from crewai import Agent, Task, Crew, LLM
from advanced_streaming_listener import advanced_streaming_listener


def test_streaming_output():
    """测试流式输出功能"""

    # 配置LLM（启用流式输出）
    llm = LLM(
        model="openai/qwen-turbo-latest",
        base_url=os.getenv("QWEN_BASE_URL"),
        api_key=os.getenv("QWEN_API_KEY"),
        temperature=0.3,
        stream=True,  # 关键：启用流式输出
    )

    # 创建代理
    analyst = Agent(
        role="系统分析师",
        goal="分析和诊断系统问题",
        backstory="你是一个经验丰富的系统分析师，擅长快速定位和解决技术问题。",
        llm=llm,
        verbose=False  # 关闭verbose，使用事件监听器
    )

    # 创建任务
    analysis_task = Task(
        description="请分析Kubernetes集群中可能出现的OOM问题，并提供解决方案。",
        expected_output="详细的问题分析报告和解决建议",
        agent=analyst
    )

    # 创建团队
    crew = Crew(
        agents=[analyst],
        tasks=[analysis_task],
        verbose=False  # 关闭verbose，使用事件监听器
    )

    print("🚀 开始测试流式输出...")
    print("=" * 60)

    # 启动任务
    result = crew.kickoff()

    print("\n" + "=" * 60)
    print("✅ 测试完成")
    return result


if __name__ == "__main__":
    test_streaming_output()
