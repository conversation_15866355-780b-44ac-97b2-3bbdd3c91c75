#!/usr/bin/env python3
"""
流式输出测试脚本
测试CrewAI的事件监听器和流式输出功能
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def test_imports():
    """测试所有必要的导入"""
    print("🧪 测试导入模块...")

    try:
        from crewai.utilities.events import (
            LLMStreamChunkEvent,
            CrewKickoffStartedEvent,
            CrewKickoffCompletedEvent,
        )
        print("✅ CrewAI事件模块导入成功")
    except ImportError as e:
        print(f"❌ CrewAI事件模块导入失败: {e}")
        return False

    try:
        from advanced_streaming_listener import advanced_streaming_listener
        print("✅ 高级流式监听器导入成功")
    except ImportError as e:
        print(f"❌ 高级流式监听器导入失败: {e}")
        return False

    try:
        from streaming_config import OUTPUT_CONFIG, DISPLAY_FORMAT
        print("✅ 流式配置导入成功")
    except ImportError as e:
        print(f"❌ 流式配置导入失败: {e}")
        return False

    return True


def test_configuration():
    """测试配置"""
    print("\n🔧 测试配置...")

    # 检查必要的环境变量
    required_vars = ["QWEN_BASE_URL", "QWEN_API_KEY"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ 环境变量配置完整")

    return True


def test_listener_registration():
    """测试监听器注册"""
    print("\n📡 测试事件监听器注册...")

    try:
        from crewai.utilities.events import crewai_event_bus

        # 检查事件总线是否可用
        if hasattr(crewai_event_bus, 'on'):
            print("✅ 事件总线可用")
        else:
            print("❌ 事件总线不可用")
            return False

        # 检查监听器是否已注册（通过检查实例是否存在）
        from advanced_streaming_listener import advanced_streaming_listener
        if advanced_streaming_listener:
            print("✅ 高级流式监听器已实例化")
        else:
            print("❌ 高级流式监听器未实例化")
            return False

    except Exception as e:
        print(f"❌ 事件监听器测试失败: {e}")
        return False

    return True


def main():
    """主测试函数"""
    print("🚀 CrewAI 流式输出系统测试")
    print("=" * 50)

    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_configuration),
        ("监听器测试", test_listener_registration),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 执行 {test_name}...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！流式输出系统就绪。")
        print("\n💡 使用提示:")
        print("1. 确保在LLM配置中设置 stream=True")
        print("2. 确保在Agent和Crew中设置 verbose=False")
        print("3. 导入 advanced_streaming_listener 以激活监听器")
        print("4. 运行 python crew.py 开始体验流式输出")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
