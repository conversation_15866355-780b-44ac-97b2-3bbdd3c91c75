"""
高级流式输出事件监听器
支持配置化的实时流式输出，提供更好的用户体验
"""
import sys
import time
from datetime import datetime
from typing import Optional, Dict, Any
from crewai.utilities.events import (
    CrewKickoffStartedEvent,
    CrewKickoffCompletedEvent,
    AgentExecutionStartedEvent,
    AgentExecutionCompletedEvent,
    TaskStartedEvent,
    TaskCompletedEvent,
    LLMStreamChunkEvent,
    LLMCallStartedEvent,
    LLMCallCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    ToolUsageErrorEvent,
)
from crewai.utilities.events.base_event_listener import BaseEventListener

try:
    from streaming_config import OUTPUT_CONFIG, EVENT_FILTER, DISPLAY_FORMAT
except ImportError:
    # 默认配置
    OUTPUT_CONFIG = {
        "show_timestamps": True,
        "show_tool_args": True,
        "max_tool_output_length": 300,
        "max_task_desc_length": 120,
        "sensitive_keywords": ["password", "token", "key", "secret", "auth"],
        "enable_colors": False,
        "stream_buffer_size": 1024,
    }
    EVENT_FILTER = {"enabled_events": [], "ignored_events": []}
    DISPLAY_FORMAT = {
        "separator_char": "=",
        "separator_length": 80,
        "sub_separator_char": "-",
        "sub_separator_length": 60,
        "icons": {
            "crew_start": "🚀", "crew_complete": "✅", "agent_start": "🤖",
            "agent_complete": "✅", "task_start": "📋", "task_complete": "✅",
            "llm_thinking": "🧠", "llm_response": "💭", "tool_start": "🔧",
            "tool_complete": "✅", "final_report": "📊", "fire": "🔥",
            "target": "🎯", "celebration": "🎉"
        }
    }


class AdvancedStreamingListener(BaseEventListener):
    """
    高级流式输出监听器
    提供可配置的实时输出和更好的格式化
    """

    def __init__(self):
        super().__init__()
        self.current_agent = None
        self.current_task = None
        self.streaming_buffer = ""
        self.start_time = None
        self.task_start_time = None
        self.llm_call_count = 0
        self.tool_usage_count = 0

    def _should_handle_event(self, event_name: str) -> bool:
        """检查是否应该处理该事件"""
        if event_name in EVENT_FILTER.get("ignored_events", []):
            return False
        enabled = EVENT_FILTER.get("enabled_events", [])
        return not enabled or event_name in enabled

    def _get_timestamp(self) -> str:
        """获取时间戳字符串"""
        if OUTPUT_CONFIG.get("show_timestamps", False):
            return f"[{datetime.now().strftime('%H:%M:%S')}] "
        return ""

    def _print_separator(self, char: Optional[str] = None, length: Optional[int] = None):
        """打印分隔符"""
        char = char or DISPLAY_FORMAT.get("separator_char", "=")
        length = length or DISPLAY_FORMAT.get("separator_length", 80)
        print(char * length)

    def _print_sub_separator(self):
        """打印子分隔符"""
        char = DISPLAY_FORMAT.get("sub_separator_char", "-")
        length = DISPLAY_FORMAT.get("sub_separator_length", 60)
        print(char * length)

    def _get_icon(self, icon_key: str) -> str:
        """获取图标"""
        return DISPLAY_FORMAT.get("icons", {}).get(icon_key, "")

    def _format_elapsed_time(self, start_time: Optional[float]) -> str:
        """格式化已用时间"""
        if start_time:
            elapsed = time.time() - start_time
            if elapsed < 60:
                return f"({elapsed:.1f}s)"
            else:
                minutes = int(elapsed // 60)
                seconds = elapsed % 60
                return f"({minutes}m {seconds:.1f}s)"
        return ""

    def _filter_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """过滤敏感数据"""
        if not isinstance(data, dict):
            return data

        filtered = {}
        sensitive_keywords = OUTPUT_CONFIG.get("sensitive_keywords", [])

        for key, value in data.items():
            key_lower = key.lower()
            if any(keyword in key_lower for keyword in sensitive_keywords):
                filtered[key] = "***[已隐藏]***"
            elif isinstance(value, str) and len(value) > 200:
                filtered[key] = value[:200] + "...[已截断]"
            else:
                filtered[key] = value

        return filtered

    def _truncate_output(self, output: str, max_length: Optional[int] = None) -> str:
        """截断过长的输出"""
        max_length = max_length or OUTPUT_CONFIG.get(
            "max_tool_output_length", 300)
        if len(output) <= max_length:
            return output

        # 显示前半部分和后半部分
        half_len = max_length // 2 - 50
        return (
            output[:half_len] +
            f"...[省略{len(output)-max_length}字符]..." +
            output[-half_len:]
        )

    def setup_listeners(self, crewai_event_bus):
        """设置事件监听器"""

        @crewai_event_bus.on(CrewKickoffStartedEvent)
        def on_crew_started(source, event):
            if not self._should_handle_event("CrewKickoffStartedEvent"):
                return

            self.start_time = time.time()
            timestamp = self._get_timestamp()

            print("\n")
            self._print_separator()
            print(f"{timestamp}{self._get_icon('crew_start')} AI-Ops SRE 智能诊断团队启动")
            if hasattr(event, 'crew_name') and event.crew_name:
                print(f"{timestamp}   团队: {event.crew_name}")
            print(
                f"{timestamp}   开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self._print_separator()

        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(source, event):
            if not self._should_handle_event("CrewKickoffCompletedEvent"):
                return

            timestamp = self._get_timestamp()
            elapsed = self._format_elapsed_time(self.start_time)

            print("\n")
            self._print_separator()
            print(f"{timestamp}{self._get_icon('crew_complete')} 智能诊断任务完成 {elapsed}")
            print(f"{timestamp}   LLM调用次数: {self.llm_call_count}")
            print(f"{timestamp}   工具使用次数: {self.tool_usage_count}")
            self._print_separator()

            if hasattr(event, 'output') and event.output:
                print(f"\n{timestamp}{self._get_icon('final_report')} 最终诊断报告:")
                self._print_sub_separator()
                print(f"{event.output}")
                self._print_sub_separator()

        @crewai_event_bus.on(AgentExecutionStartedEvent)
        def on_agent_started(source, event):
            if not self._should_handle_event("AgentExecutionStartedEvent"):
                return

            if hasattr(event, 'agent') and event.agent:
                self.current_agent = event.agent.role
                timestamp = self._get_timestamp()

                print(
                    f"\n{timestamp}{self._get_icon('agent_start')} 代理 [{self.current_agent}] 开始工作")
                self._print_sub_separator()

        @crewai_event_bus.on(AgentExecutionCompletedEvent)
        def on_agent_completed(source, event):
            if not self._should_handle_event("AgentExecutionCompletedEvent"):
                return

            if hasattr(event, 'agent') and event.agent:
                timestamp = self._get_timestamp()
                print(
                    f"\n{timestamp}{self._get_icon('agent_complete')} 代理 [{event.agent.role}] 工作完成")
                self._print_sub_separator()

        @crewai_event_bus.on(TaskStartedEvent)
        def on_task_started(source, event):
            if not self._should_handle_event("TaskStartedEvent"):
                return

            if hasattr(event, 'task') and event.task:
                self.task_start_time = time.time()
                timestamp = self._get_timestamp()

                task_desc = event.task.description
                max_length = OUTPUT_CONFIG.get("max_task_desc_length", 120)
                if len(task_desc) > max_length:
                    task_desc = task_desc[:max_length] + "..."

                print(
                    f"\n{timestamp}{self._get_icon('task_start')} 任务: {task_desc}")

                if hasattr(event.task, 'expected_output'):
                    expected = event.task.expected_output
                    if len(expected) > 100:
                        expected = expected[:100] + "..."
                    print(f"{timestamp}   期望输出: {expected}")

        @crewai_event_bus.on(TaskCompletedEvent)
        def on_task_completed(source, event):
            if not self._should_handle_event("TaskCompletedEvent"):
                return

            timestamp = self._get_timestamp()
            elapsed = self._format_elapsed_time(self.task_start_time)
            print(f"{timestamp}{self._get_icon('task_complete')} 任务完成 {elapsed}")

        @crewai_event_bus.on(LLMCallStartedEvent)
        def on_llm_call_started(source, event):
            if not self._should_handle_event("LLMCallStartedEvent"):
                return

            self.llm_call_count += 1
            timestamp = self._get_timestamp()
            print(f"\n{timestamp}{self._get_icon('llm_thinking')} AI思考中...")
            sys.stdout.flush()

        @crewai_event_bus.on(LLMStreamChunkEvent)
        def on_llm_stream_chunk(source, event):
            if not self._should_handle_event("LLMStreamChunkEvent"):
                return

            if hasattr(event, 'chunk') and event.chunk:
                # 如果是流式输出的开始，显示提示
                if not self.streaming_buffer:
                    timestamp = self._get_timestamp()
                    print(
                        f"{timestamp}{self._get_icon('llm_response')} AI回应: ", end='', flush=True)

                # 输出流式内容
                chunk_text = str(event.chunk)
                print(chunk_text, end='', flush=True)
                self.streaming_buffer += chunk_text

                # 如果缓冲区过大，清空一部分
                buffer_size = OUTPUT_CONFIG.get("stream_buffer_size", 1024)
                if len(self.streaming_buffer) > buffer_size:
                    self.streaming_buffer = self.streaming_buffer[-buffer_size//2:]

        @crewai_event_bus.on(LLMCallCompletedEvent)
        def on_llm_call_completed(source, event):
            if not self._should_handle_event("LLMCallCompletedEvent"):
                return

            if self.streaming_buffer:
                print()  # 换行
                self.streaming_buffer = ""
            sys.stdout.flush()

        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(source, event):
            if not self._should_handle_event("ToolUsageStartedEvent"):
                return

            if hasattr(event, 'tool_name'):
                self.tool_usage_count += 1
                timestamp = self._get_timestamp()
                print(
                    f"\n{timestamp}{self._get_icon('tool_start')} 使用工具: {event.tool_name}")

                # 显示工具参数
                if (OUTPUT_CONFIG.get("show_tool_args", True) and
                        hasattr(event, 'input_args') and event.input_args):
                    filtered_args = self._filter_sensitive_data(
                        event.input_args)
                    print(f"{timestamp}   参数: {filtered_args}")

        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(source, event):
            if not self._should_handle_event("ToolUsageFinishedEvent"):
                return

            if hasattr(event, 'tool_name'):
                timestamp = self._get_timestamp()
                print(
                    f"{timestamp}{self._get_icon('tool_complete')} 工具完成: {event.tool_name}")

                # 显示工具输出
                if hasattr(event, 'output') and event.output:
                    output_str = str(event.output)
                    truncated_output = self._truncate_output(output_str)
                    print(f"{timestamp}   结果: {truncated_output}")

        @crewai_event_bus.on(ToolUsageErrorEvent)
        def on_tool_error(source, event):
            if hasattr(event, 'tool_name') and hasattr(event, 'error'):
                timestamp = self._get_timestamp()
                print(f"{timestamp}❌ 工具错误: {event.tool_name}")
                print(f"{timestamp}   错误: {str(event.error)[:200]}")


# 创建高级监听器实例
advanced_streaming_listener = AdvancedStreamingListener()
