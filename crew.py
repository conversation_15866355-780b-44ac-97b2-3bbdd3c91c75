import os
import yaml
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process, LLM
from crewai_tools import MCPServerAdapter

# 导入高级流式输出监听器
from advanced_streaming_listener import advanced_streaming_listener

load_dotenv()


def load_yaml(path: str) -> dict:
    with open(path, 'r') as file:
        return yaml.safe_load(file)


def get_inputs_from_env() -> dict:
    """从环境变量加载配置"""
    inputs = {
        "namespace": os.getenv("NAMESPACE", "default"),
        "pod_name": os.getenv("POD_NAME"),
        "time_range": os.getenv("TIME_RANGE", "2h"),
        "clusterID": os.getenv("K8S_CLUSTER_ID"),
        "nodePoolID": os.getenv("K8S_NODEPOOL_ID"),
        "sls_project": os.getenv("SLS_PROJECT"),
        "sls_logstore": os.getenv("SLS_LOGSTORE"),
        "arms_project": os.getenv("ARMS_PROJECT"),
        "arms_metricstore": os.getenv("ARMS_METRICSTORE"),
        "regionId": os.getenv("REGION_ID"),
    }
    return inputs


agents_config = load_yaml('config/agents.yaml')
tasks_config = load_yaml('config/tasks.yaml')


def run_oom_investigation():
    """
    运行 OOMKill 问题排查流程。
    """
    question = input("请输入您要排查的问题: ")
    if not question:
        print("未输入问题，程序退出。")
        return

    inputs = get_inputs_from_env()
    inputs['question'] = question

    print("## 欢迎使用 AI-Ops SRE 团队（智能诊断版）")
    print("-------------------------------------------------------------")
    print(f"开始排查命名空间 '{inputs['namespace']}' 的OOM问题...")
    if inputs.get('pod_name'):
        print(f"重点关注Pod: {inputs['pod_name']}")

    server_lists = [
        # k8s mcp server
        {
            "url": "http://localhost:3000/sse",
            "transport": "sse"
        },
        # obs mcp server
        {
            "url": "http://localhost:8000/sse",
            "transport": "sse"
        },
        {
            "url": "https://openapi-mcp.cn-hangzhou.aliyuncs.com/accounts/****************/custom/openapi-mcp-serv/id/t5XBMeaHqlSnZLJg/sse",
            "transport": "sse",
            "headers": {
                "Authorization": f"Bearer {os.getenv('ACCESS_TOKEN')}"
            }
        }
    ]

    with MCPServerAdapter(server_lists) as mcp_tools:
        print("MCP 服务器连接已建立。")

        qwen_llm = LLM(
            model="openai/qwen-turbo-latest",
            base_url=os.getenv("QWEN_BASE_URL"),
            api_key=os.getenv("QWEN_API_KEY"),
            temperature=0.3,
            # 启用流式输出以配合事件监听器
            stream=True,
        )

        print(f"Available tools: {[tool.name for tool in mcp_tools]}")

        # 定义工具集
        tool_mapping = {
            'k8s_diagnoser': [
                mcp_tools["kubectl_get"], mcp_tools["kubectl_describe"],
                mcp_tools["kubectl_logs"]
            ],
            'sls_diagnoser': [
                mcp_tools["sls_execute_sql_query"],
                mcp_tools["sls_translate_text_to_sql_query"],
                mcp_tools["sls_diagnose_query"]
            ],
            'arms_diagnoser': [
                mcp_tools["cms_execute_promql_query"],
                mcp_tools["cms_translate_text_to_promql"],
                mcp_tools["arms_search_apps"]
            ],
            'cluster_operator': [
                mcp_tools["kubectl_patch"],
                mcp_tools["CS-20151215-ScaleClusterNodePool"]
            ]
        }

        # 创建代理
        agents = {}
        for agent_name, agent_config in agents_config.items():
            agent_tools = tool_mapping.get(agent_name)
            agents[agent_name] = Agent(
                **agent_config,
                tools=agent_tools,
                llm=qwen_llm,
                # 移除verbose，使用事件监听器进行流式输出
                verbose=False
            )

        # 创建任务
        k8s_task = Task(
            **tasks_config['k8s_investigation_task'], agent=agents['k8s_diagnoser'])
        sls_task = Task(
            **tasks_config['sls_log_analysis_task'], agent=agents['sls_diagnoser'])
        arms_task = Task(
            **tasks_config['arms_metric_analysis_task'], agent=agents['arms_diagnoser'])
        analysis_task = Task(**tasks_config['analysis_propose_task'],
                             agent=agents['sre_supervisor'], context=[k8s_task, sls_task, arms_task])
        report_task = Task(**tasks_config['report_generation_task'],
                           agent=agents['report_generator'], context=[analysis_task])

        # 组装团队
        crew = Crew(
            agents=list(agents.values()),
            tasks=[k8s_task, sls_task, arms_task, analysis_task, report_task],
            process=Process.hierarchical,
            manager_llm=qwen_llm,
            planning=True,
            planning_llm=qwen_llm,
            # 移除verbose，使用事件监听器进行流式输出
            verbose=False
        )

        print(f"\n🔥 启动AI-Ops SRE智能诊断团队...")
        print(f"📊 诊断参数: {inputs}")
        print("🎯 团队将通过事件监听器提供实时流式输出\n")
        result = crew.kickoff(inputs=inputs)

        print("\n\n## 🎉 智能诊断完成！")
        print("-" * 50)
        print("感谢使用 AI-Ops SRE 智能诊断系统")


if __name__ == "__main__":
    run_oom_investigation()
