### **项目名称: AI-Ops Supervisor Crew**

**目标**: 构建一个基于 CrewAI 的自动化运维机器人，采用 Supervisor-Worker 模式，通过 MCP 协议集成外部运维工具（k8s, SLS, ARMS 等），并使用 Qwen 模型作为 LLM。

---

#### **1. 核心架构**

我们将采用 CrewAI 的**层级化流程 (Hierarchical Process)** 来实现 Supervisor-Worker 架构。

```mermaid
graph TD
    A[用户请求: "诊断 pod-xyz 失败"] --> B{Supervisor Agent / Planner};
    B -- 制定计划, 分配任务 --> C[k8s 诊断 Agent];
    B -- 分配任务 --> D[SLS 诊断 Agent];
    B -- 分配任务 --> E[ARMS 诊断 Agent];
    
    subgraph "Worker Agents"
        C -- 使用 k8s 工具 --> F[(k8s 工具集)];
        D -- 使用 SLS 工具 --> G[(SLS 工具)];
        E -- 使用 ARMS 工具 --> H[(ARMS 工具)];
    end

    C -- 返回诊断信息 --> B;
    D -- 返回日志摘要 --> B;
    E -- 返回监控数据 --> B;

    B -- 综合信息, 决策 --> I{执行 Agent};
    I -- 使用操作工具 --> J[(集群扩容工具)];
    I -- 返回执行结果 --> K[最终报告];
    B -- 生成最终报告 --> K;
```

*   **Supervisor Agent (Planner)**：作为 Crew 的管理者 (`manager_llm`)。它不直接执行工具，而是负责理解用户意图、拆分任务、委派给合适的 Worker Agent，并综合结果，最终形成结论或执行下一步操作。
*   **Worker Agents**：
    *   **K8sDiagnoser**: 专门负责与 k8s 相关的诊断，被授权使用 `k8s_get`, `k8s_log`, `k8s_describe` 工具。
    *   **SLSInspector**: 专门负责查询 SLS 日志，被授权使用 `query_sls` 工具。
    *   **ARMSWatcher**: 专门负责查询 ARMS 监控，被授权使用 `query_arms` 工具。
    *   **Operator**: 专门负责执行变更操作，被授权使用 `scale_cluster` 工具。
*   **工具 (Tools)**：通过单个 **MCP Server** 提供，该 Server 是一个本地 Python 脚本，通过 `Stdio` 方式与 CrewAI 通信。这大大简化了工具的管理和集成。

---

#### **2. 实施步骤详解**

**第一步: 项目初始化与环境设置 (TBD)**

*   创建项目目录结构。
*   初始化 Python 虚拟环境。
*   安装必要的依赖:
    *   `crewai`
    *   `'crewai-tools[mcp]'`
    *   `python-dotenv` (用于管理 API Key 等)

**第二步: 创建 MCP 工具服务器 (TBD)**

*   创建一个 `mcp_server.py` 文件。
*   在这个文件中，使用 `crewai_tools` 的 `@tool` 装饰器定义四个（或更多）工具函数：
    *   `k8s_tool(operation: str, resource: str)`
    *   `sls_query_tool(query: str, time_range: str)`
    *   `arms_query_tool(metric: str, resource_id: str)`
    *   `cluster_scale_tool(replicas: int)`
    *   **注意**: 这些函数内部只是 **mock** 逻辑（例如，返回固定的字符串说明 "成功调用了 k8s get pod xxx"），真正的实现逻辑由你后续填充。这样可以保证我们的代码行数少于1000行，并专注于流程。
*   在此文件的末尾，启动 MCP Stdio 服务器。

**第三步: 定义 CrewAI 的 Agents (TBD)**

*   创建一个 `crew.py` 文件。
*   **配置 LLM**: 在文件开头，配置使用 Qwen。
    ```python
    import os
    from crewai import Agent, LLM

    # 优先从环境变量读取，如果不存在则使用默认值
    qwen_llm = LLM(
        model=os.getenv("OPENAI_MODEL_NAME", "qwen-turbo"),
        base_url=os.getenv("OPENAI_API_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
        api_key=os.getenv("DASHSCOPE_API_KEY")
    )
    ```
*   **定义 Supervisor**: 将 `qwen_llm` 作为 `manager_llm` 配置到 Crew 中。
*   **定义 Workers**: 定义 `K8sDiagnoser`, `SLSInspector`, `ARMSWatcher`, `Operator` 四个 `Agent`。每个 Agent 都有清晰的 `role`, `goal`, 和 `backstory`。最关键的是，它们的 `tools` 参数将通过 `MCPServerAdapter` 注入，并且会进行过滤。

**第四步: 定义 CrewAI 的 Tasks (TBD)**

*   在 `crew.py` 中，定义与 Agents 对应的 `Task`。
*   Task 的 `description` 将会非常泛化，允许 Agent 根据实际情况决定调用工具的次数和顺序。例如，给 `K8sDiagnoser` 的任务可能是：“诊断 Pod [pod_name] 的健康状况。收集所有必要的信息，包括它的状态、事件和最近的日志。”
*   Agent 被设计为可以重复调用工具，它的 `reasoning` 过程会让它判断是否需要更多信息。

**第五步: 组装并运行 Crew (TBD)**

*   在 `crew.py` 的主函数部分：
    *   实例化 `MCPServerAdapter`，连接到 `mcp_server.py`。
    *   实例化所有 Agents，并通过工具过滤给它们分配各自的工具。
    *   实例化所有 Tasks。
    *   创建 `Crew` 对象，设置 `process` 为 `Process.hierarchical`，并指定 `manager_llm`。
    *   调用 `crew.kickoff()` 启动任务。

**第六步: 编写部署和使用说明 (TBD)**

*   创建一个 `README.md` 文件。
*   说明如何设置环境变量（如 `DASHSCOPE_API_KEY`）。
*   说明如何运行程序：
    1.  `python crew.py`
*   提供一个输入示例，以及预期的输出格式。

---

这个计划满足了你的所有要求：

*   **MCP 工具集成**: ✔️
*   **工具过滤**: ✔️
*   **Supervisor 架构**: ✔️ (Hierarchical Process)
*   **Qwen LLM**: ✔️
*   **代码行数 < 1000**: ✔️ (通过 mock 工具实现，聚焦核心流程)
*   **避免复杂编排**: ✔️ (使用内置的层级流程，而非自定义复杂逻辑)
*   **重复调用工具**: ✔️ (这是 CrewAI Agent 的内置能力)