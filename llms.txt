# CrewAI

## Docs

- [Get Execution Status](https://docs.crewai.com/api-reference/get-execution-status.md): **📋 Reference Example Only** - *This shows the request format. To test with your actual crew, copy the cURL example and replace the URL + token with your real values.*

Retrieves the current status and results of a crew execution using its kickoff ID.

The response structure varies depending on the execution state:
- **running**: Execution in progress with current task info
- **completed**: Execution finished with full results
- **error**: Execution failed with error details

- [Get Required Inputs](https://docs.crewai.com/api-reference/get-required-inputs.md): **📋 Reference Example Only** - *This shows the request format. To test with your actual crew, copy the cURL example and replace the URL + token with your real values.*

Retrieves the list of all required input parameters that your crew expects for execution.
Use this endpoint to discover what inputs you need to provide when starting a crew execution.

- [Start Crew Execution](https://docs.crewai.com/api-reference/start-crew-execution.md): **📋 Reference Example Only** - *This shows the request format. To test with your actual crew, copy the cURL example and replace the URL + token with your real values.*

Initiates a new crew execution with the provided inputs. Returns a kickoff ID that can be used 
to track the execution progress and retrieve results.

Crew executions can take anywhere from seconds to minutes depending on their complexity.
Consider using webhooks for real-time notifications or implement polling with the status endpoint.

- [Introduction](https://docs.crewai.com/en/api-reference/introduction.md): Complete reference for the CrewAI Enterprise REST API
- [Agents](https://docs.crewai.com/en/concepts/agents.md): Detailed guide on creating and managing agents within the CrewAI framework.
- [CLI](https://docs.crewai.com/en/concepts/cli.md): Learn how to use the CrewAI CLI to interact with CrewAI.
- [Collaboration](https://docs.crewai.com/en/concepts/collaboration.md): How to enable agents to work together, delegate tasks, and communicate effectively within CrewAI teams.
- [Crews](https://docs.crewai.com/en/concepts/crews.md): Understanding and utilizing crews in the crewAI framework with comprehensive attributes and functionalities.
- [Event Listeners](https://docs.crewai.com/en/concepts/event-listener.md): Tap into CrewAI events to build custom integrations and monitoring
- [Flows](https://docs.crewai.com/en/concepts/flows.md): Learn how to create and manage AI workflows using CrewAI Flows.
- [Knowledge](https://docs.crewai.com/en/concepts/knowledge.md): What is knowledge in CrewAI and how to use it.
- [LLMs](https://docs.crewai.com/en/concepts/llms.md): A comprehensive guide to configuring and using Large Language Models (LLMs) in your CrewAI projects
- [Memory](https://docs.crewai.com/en/concepts/memory.md): Leveraging memory systems in the CrewAI framework to enhance agent capabilities.
- [Planning](https://docs.crewai.com/en/concepts/planning.md): Learn how to add planning to your CrewAI Crew and improve their performance.
- [Processes](https://docs.crewai.com/en/concepts/processes.md): Detailed guide on workflow management through processes in CrewAI, with updated implementation details.
- [Reasoning](https://docs.crewai.com/en/concepts/reasoning.md): Learn how to enable and use agent reasoning to improve task execution.
- [Tasks](https://docs.crewai.com/en/concepts/tasks.md): Detailed guide on managing and creating tasks within the CrewAI framework.
- [Testing](https://docs.crewai.com/en/concepts/testing.md): Learn how to test your CrewAI Crew and evaluate their performance.
- [Tools](https://docs.crewai.com/en/concepts/tools.md): Understanding and leveraging tools within the CrewAI framework for agent collaboration and task execution.
- [Training](https://docs.crewai.com/en/concepts/training.md): Learn how to train your CrewAI agents by giving them feedback early on and get consistent results.
- [Agent Repositories](https://docs.crewai.com/en/enterprise/features/agent-repositories.md): Learn how to use Agent Repositories to share and reuse your agents across teams and projects
- [Hallucination Guardrail](https://docs.crewai.com/en/enterprise/features/hallucination-guardrail.md): Prevent and detect AI hallucinations in your CrewAI tasks
- [Integrations](https://docs.crewai.com/en/enterprise/features/integrations.md): Connected applications for your agents to take actions.
- [Tool Repository](https://docs.crewai.com/en/enterprise/features/tool-repository.md): Using the Tool Repository to manage your tools
- [Traces](https://docs.crewai.com/en/enterprise/features/traces.md): Using Traces to monitor your Crews
- [Webhook Streaming](https://docs.crewai.com/en/enterprise/features/webhook-streaming.md): Using Webhook Streaming to stream events to your webhook
- [Azure OpenAI Setup](https://docs.crewai.com/en/enterprise/guides/azure-openai-setup.md): Configure Azure OpenAI with Crew Studio for enterprise LLM connections
- [Build Crew](https://docs.crewai.com/en/enterprise/guides/build-crew.md): A Crew is a group of agents that work together to complete a task.
- [Deploy Crew](https://docs.crewai.com/en/enterprise/guides/deploy-crew.md): Deploying a Crew on CrewAI Enterprise
- [Enable Crew Studio](https://docs.crewai.com/en/enterprise/guides/enable-crew-studio.md): Enabling Crew Studio on CrewAI Enterprise
- [HubSpot Trigger](https://docs.crewai.com/en/enterprise/guides/hubspot-trigger.md): Trigger CrewAI crews directly from HubSpot Workflows
- [HITL Workflows](https://docs.crewai.com/en/enterprise/guides/human-in-the-loop.md): Learn how to implement Human-In-The-Loop workflows in CrewAI for enhanced decision-making
- [Kickoff Crew](https://docs.crewai.com/en/enterprise/guides/kickoff-crew.md): Kickoff a Crew on CrewAI Enterprise
- [React Component Export](https://docs.crewai.com/en/enterprise/guides/react-component-export.md): Learn how to export and integrate CrewAI Enterprise React components into your applications
- [Salesforce Trigger](https://docs.crewai.com/en/enterprise/guides/salesforce-trigger.md): Trigger CrewAI crews from Salesforce workflows for CRM automation
- [Slack Trigger](https://docs.crewai.com/en/enterprise/guides/slack-trigger.md): Trigger CrewAI crews directly from Slack using slash commands
- [Team Management](https://docs.crewai.com/en/enterprise/guides/team-management.md): Learn how to invite and manage team members in your CrewAI Enterprise organization
- [Update Crew](https://docs.crewai.com/en/enterprise/guides/update-crew.md): Updating a Crew on CrewAI Enterprise
- [Webhook Automation](https://docs.crewai.com/en/enterprise/guides/webhook-automation.md): Automate CrewAI Enterprise workflows using webhooks with platforms like ActivePieces, Zapier, and Make.com
- [Zapier Trigger](https://docs.crewai.com/en/enterprise/guides/zapier-trigger.md): Trigger CrewAI crews from Zapier workflows to automate cross-app workflows
- [Asana Integration](https://docs.crewai.com/en/enterprise/integrations/asana.md): Team task and project coordination with Asana integration for CrewAI.
- [Box Integration](https://docs.crewai.com/en/enterprise/integrations/box.md): File storage and document management with Box integration for CrewAI.
- [ClickUp Integration](https://docs.crewai.com/en/enterprise/integrations/clickup.md): Task and productivity management with ClickUp integration for CrewAI.
- [GitHub Integration](https://docs.crewai.com/en/enterprise/integrations/github.md): Repository and issue management with GitHub integration for CrewAI.
- [Gmail Integration](https://docs.crewai.com/en/enterprise/integrations/gmail.md): Email and contact management with Gmail integration for CrewAI.
- [Google Calendar Integration](https://docs.crewai.com/en/enterprise/integrations/google_calendar.md): Event and schedule management with Google Calendar integration for CrewAI.
- [Google Sheets Integration](https://docs.crewai.com/en/enterprise/integrations/google_sheets.md): Spreadsheet data synchronization with Google Sheets integration for CrewAI.
- [HubSpot Integration](https://docs.crewai.com/en/enterprise/integrations/hubspot.md): Manage companies and contacts in HubSpot with CrewAI.
- [Jira Integration](https://docs.crewai.com/en/enterprise/integrations/jira.md): Issue tracking and project management with Jira integration for CrewAI.
- [Linear Integration](https://docs.crewai.com/en/enterprise/integrations/linear.md): Software project and bug tracking with Linear integration for CrewAI.
- [Notion Integration](https://docs.crewai.com/en/enterprise/integrations/notion.md): Page and database management with Notion integration for CrewAI.
- [Salesforce Integration](https://docs.crewai.com/en/enterprise/integrations/salesforce.md): CRM and sales automation with Salesforce integration for CrewAI.
- [Shopify Integration](https://docs.crewai.com/en/enterprise/integrations/shopify.md): E-commerce and online store management with Shopify integration for CrewAI.
- [Slack Integration](https://docs.crewai.com/en/enterprise/integrations/slack.md): Team communication and collaboration with Slack integration for CrewAI.
- [Stripe Integration](https://docs.crewai.com/en/enterprise/integrations/stripe.md): Payment processing and subscription management with Stripe integration for CrewAI.
- [Zendesk Integration](https://docs.crewai.com/en/enterprise/integrations/zendesk.md): Customer support and helpdesk management with Zendesk integration for CrewAI.
- [CrewAI Enterprise](https://docs.crewai.com/en/enterprise/introduction.md): Deploy, monitor, and scale your AI agent workflows
- [FAQs](https://docs.crewai.com/en/enterprise/resources/frequently-asked-questions.md): Frequently asked questions about CrewAI Enterprise
- [CrewAI Examples](https://docs.crewai.com/en/examples/example.md): A collection of examples that show how to use CrewAI framework to automate workflows.
- [Customizing Prompts](https://docs.crewai.com/en/guides/advanced/customizing-prompts.md): Dive deeper into low-level prompt customization for CrewAI, enabling super custom and complex use cases for different models and languages.
- [Fingerprinting](https://docs.crewai.com/en/guides/advanced/fingerprinting.md): Learn how to use CrewAI's fingerprinting system to uniquely identify and track components throughout their lifecycle.
- [Crafting Effective Agents](https://docs.crewai.com/en/guides/agents/crafting-effective-agents.md): Learn best practices for designing powerful, specialized AI agents that collaborate effectively to solve complex problems.
- [Evaluating Use Cases for CrewAI](https://docs.crewai.com/en/guides/concepts/evaluating-use-cases.md): Learn how to assess your AI application needs and choose the right approach between Crews and Flows based on complexity and precision requirements.
- [Build Your First Crew](https://docs.crewai.com/en/guides/crews/first-crew.md): Step-by-step tutorial to create a collaborative AI team that works together to solve complex problems.
- [Build Your First Flow](https://docs.crewai.com/en/guides/flows/first-flow.md): Learn how to create structured, event-driven workflows with precise control over execution.
- [Mastering Flow State Management](https://docs.crewai.com/en/guides/flows/mastering-flow-state.md): A comprehensive guide to managing, persisting, and leveraging state in CrewAI Flows for building robust AI applications.
- [Installation](https://docs.crewai.com/en/installation.md): Get started with CrewAI - Install, configure, and build your first AI crew
- [Introduction](https://docs.crewai.com/en/introduction.md): Build AI agent teams that work together to tackle complex tasks
- [Before and After Kickoff Hooks](https://docs.crewai.com/en/learn/before-and-after-kickoff-hooks.md): Learn how to use before and after kickoff hooks in CrewAI
- [Bring your own agent](https://docs.crewai.com/en/learn/bring-your-own-agent.md): Learn how to bring your own agents that work within a Crew.
- [Coding Agents](https://docs.crewai.com/en/learn/coding-agents.md): Learn how to enable your CrewAI Agents to write and execute code, and explore advanced features for enhanced functionality.
- [Conditional Tasks](https://docs.crewai.com/en/learn/conditional-tasks.md): Learn how to use conditional tasks in a crewAI kickoff
- [Create Custom Tools](https://docs.crewai.com/en/learn/create-custom-tools.md): Comprehensive guide on crafting, using, and managing custom tools within the CrewAI framework, including new functionalities and error handling.
- [Custom LLM Implementation](https://docs.crewai.com/en/learn/custom-llm.md): Learn how to create custom LLM implementations in CrewAI.
- [Custom Manager Agent](https://docs.crewai.com/en/learn/custom-manager-agent.md): Learn how to set a custom agent as the manager in CrewAI, providing more control over task management and coordination.
- [Customize Agents](https://docs.crewai.com/en/learn/customizing-agents.md): A comprehensive guide to tailoring agents for specific roles, tasks, and advanced customizations within the CrewAI framework.
- [Image Generation with DALL-E](https://docs.crewai.com/en/learn/dalle-image-generation.md): Learn how to use DALL-E for AI-powered image generation in your CrewAI projects
- [Force Tool Output as Result](https://docs.crewai.com/en/learn/force-tool-output-as-result.md): Learn how to force tool output as the result in an Agent's task in CrewAI.
- [Hierarchical Process](https://docs.crewai.com/en/learn/hierarchical-process.md): A comprehensive guide to understanding and applying the hierarchical process within your CrewAI projects, updated to reflect the latest coding practices and functionalities.
- [Human-in-the-Loop (HITL) Workflows](https://docs.crewai.com/en/learn/human-in-the-loop.md): Learn how to implement Human-in-the-Loop workflows in CrewAI for enhanced decision-making
- [Human Input on Execution](https://docs.crewai.com/en/learn/human-input-on-execution.md): Integrating CrewAI with human input during execution in complex decision-making processes and leveraging the full capabilities of the agent's attributes and tools.
- [Kickoff Crew Asynchronously](https://docs.crewai.com/en/learn/kickoff-async.md): Kickoff a Crew Asynchronously
- [Kickoff Crew for Each](https://docs.crewai.com/en/learn/kickoff-for-each.md): Kickoff Crew for Each Item in a List
- [Connect to any LLM](https://docs.crewai.com/en/learn/llm-connections.md): Comprehensive guide on integrating CrewAI with various Large Language Models (LLMs) using LiteLLM, including supported providers and configuration options.
- [Strategic LLM Selection Guide](https://docs.crewai.com/en/learn/llm-selection-guide.md): Strategic framework for choosing the right LLM for your CrewAI AI agents and writing effective task and agent definitions
- [Using Multimodal Agents](https://docs.crewai.com/en/learn/multimodal-agents.md): Learn how to enable and use multimodal capabilities in your agents for processing images and other non-text content within the CrewAI framework.
- [Overview](https://docs.crewai.com/en/learn/overview.md): Learn how to build, customize, and optimize your CrewAI applications with comprehensive guides and tutorials
- [Replay Tasks from Latest Crew Kickoff](https://docs.crewai.com/en/learn/replay-tasks-from-latest-crew-kickoff.md): Replay tasks from the latest crew.kickoff(...)
- [Sequential Processes](https://docs.crewai.com/en/learn/sequential-process.md): A comprehensive guide to utilizing the sequential processes for task execution in CrewAI projects.
- [Using Annotations in crew.py](https://docs.crewai.com/en/learn/using-annotations.md): Learn how to use annotations to properly structure agents, tasks, and components in CrewAI
- [Connecting to Multiple MCP Servers](https://docs.crewai.com/en/mcp/multiple-servers.md): Learn how to use MCPServerAdapter in CrewAI to connect to multiple MCP servers simultaneously and aggregate their tools.
- [MCP Servers as Tools in CrewAI](https://docs.crewai.com/en/mcp/overview.md): Learn how to integrate MCP servers as tools in your CrewAI agents using the `crewai-tools` library.
- [MCP Security Considerations](https://docs.crewai.com/en/mcp/security.md): Learn about important security best practices when integrating MCP servers with your CrewAI agents.
- [SSE Transport](https://docs.crewai.com/en/mcp/sse.md): Learn how to connect CrewAI to remote MCP servers using Server-Sent Events (SSE) for real-time communication.
- [Stdio Transport](https://docs.crewai.com/en/mcp/stdio.md): Learn how to connect CrewAI to local MCP servers using the Stdio (Standard Input/Output) transport mechanism.
- [Streamable HTTP Transport](https://docs.crewai.com/en/mcp/streamable-http.md): Learn how to connect CrewAI to remote MCP servers using the flexible Streamable HTTP transport.
- [AgentOps Integration](https://docs.crewai.com/en/observability/agentops.md): Understanding and logging your agent performance with AgentOps.
- [Arize Phoenix](https://docs.crewai.com/en/observability/arize-phoenix.md): Arize Phoenix integration for CrewAI with OpenTelemetry and OpenInference
- [Langfuse Integration](https://docs.crewai.com/en/observability/langfuse.md): Learn how to integrate Langfuse with CrewAI via OpenTelemetry using OpenLit
- [Langtrace Integration](https://docs.crewai.com/en/observability/langtrace.md): How to monitor cost, latency, and performance of CrewAI Agents using Langtrace, an external observability tool.
- [Maxim Integration](https://docs.crewai.com/en/observability/maxim.md): Start Agent monitoring, evaluation, and observability
- [MLflow Integration](https://docs.crewai.com/en/observability/mlflow.md): Quickly start monitoring your Agents with MLflow.
- [Neatlogs Integration](https://docs.crewai.com/en/observability/neatlogs.md): Understand, debug, and share your CrewAI agent runs
- [OpenLIT Integration](https://docs.crewai.com/en/observability/openlit.md): Quickly start monitoring your Agents in just a single line of code with OpenTelemetry.
- [Opik Integration](https://docs.crewai.com/en/observability/opik.md): Learn how to use Comet Opik to debug, evaluate, and monitor your CrewAI applications with comprehensive tracing, automated evaluations, and production-ready dashboards.
- [Overview](https://docs.crewai.com/en/observability/overview.md): Monitor, evaluate, and optimize your CrewAI agents with comprehensive observability tools
- [Patronus AI Evaluation](https://docs.crewai.com/en/observability/patronus-evaluation.md): Monitor and evaluate CrewAI agent performance using Patronus AI's comprehensive evaluation platform for LLM outputs and agent behaviors.
- [Portkey Integration](https://docs.crewai.com/en/observability/portkey.md): How to use Portkey with CrewAI
- [Weave Integration](https://docs.crewai.com/en/observability/weave.md): Learn how to use Weights & Biases (W&B) Weave to track, experiment with, evaluate, and improve your CrewAI applications.
- [Quickstart](https://docs.crewai.com/en/quickstart.md): Build your first AI agent with CrewAI in under 5 minutes.
- [null](https://docs.crewai.com/en/snippets/snippet-intro.md)
- [Telemetry](https://docs.crewai.com/en/telemetry.md): Understanding the telemetry data collected by CrewAI and how it contributes to the enhancement of the library.
- [AI Mind Tool](https://docs.crewai.com/en/tools/ai-ml/aimindtool.md): The `AIMindTool` is designed to query data sources in natural language.
- [Code Interpreter](https://docs.crewai.com/en/tools/ai-ml/codeinterpretertool.md): The `CodeInterpreterTool` is a powerful tool designed for executing Python 3 code within a secure, isolated environment.
- [DALL-E Tool](https://docs.crewai.com/en/tools/ai-ml/dalletool.md): The `DallETool` is a powerful tool designed for generating images from textual descriptions.
- [LangChain Tool](https://docs.crewai.com/en/tools/ai-ml/langchaintool.md): The `LangChainTool` is a wrapper for LangChain tools and query engines.
- [LlamaIndex Tool](https://docs.crewai.com/en/tools/ai-ml/llamaindextool.md): The `LlamaIndexTool` is a wrapper for LlamaIndex tools and query engines.
- [Overview](https://docs.crewai.com/en/tools/ai-ml/overview.md): Leverage AI services, generate images, process vision, and build intelligent systems
- [RAG Tool](https://docs.crewai.com/en/tools/ai-ml/ragtool.md): The `RagTool` is a dynamic knowledge base tool for answering questions using Retrieval-Augmented Generation.
- [Vision Tool](https://docs.crewai.com/en/tools/ai-ml/visiontool.md): The `VisionTool` is designed to extract text from images.
- [Apify Actors](https://docs.crewai.com/en/tools/automation/apifyactorstool.md): `ApifyActorsTool` lets you call Apify Actors to provide your CrewAI workflows with web scraping, crawling, data extraction, and web automation capabilities.
- [Composio Tool](https://docs.crewai.com/en/tools/automation/composiotool.md): Composio provides 250+ production-ready tools for AI agents with flexible authentication management.
- [MultiOn Tool](https://docs.crewai.com/en/tools/automation/multiontool.md): The `MultiOnTool` empowers CrewAI agents with the capability to navigate and interact with the web through natural language instructions.
- [Overview](https://docs.crewai.com/en/tools/automation/overview.md): Automate workflows and integrate with external platforms and services
- [Bedrock Invoke Agent Tool](https://docs.crewai.com/en/tools/cloud-storage/bedrockinvokeagenttool.md): Enables CrewAI agents to invoke Amazon Bedrock Agents and leverage their capabilities within your workflows
- [Bedrock Knowledge Base Retriever](https://docs.crewai.com/en/tools/cloud-storage/bedrockkbretriever.md): Retrieve information from Amazon Bedrock Knowledge Bases using natural language queries
- [Overview](https://docs.crewai.com/en/tools/cloud-storage/overview.md): Interact with cloud services, storage systems, and cloud-based AI platforms
- [S3 Reader Tool](https://docs.crewai.com/en/tools/cloud-storage/s3readertool.md): The `S3ReaderTool` enables CrewAI agents to read files from Amazon S3 buckets.
- [S3 Writer Tool](https://docs.crewai.com/en/tools/cloud-storage/s3writertool.md): The `S3WriterTool` enables CrewAI agents to write content to files in Amazon S3 buckets.
- [MySQL RAG Search](https://docs.crewai.com/en/tools/database-data/mysqltool.md): The `MySQLSearchTool` is designed to search MySQL databases and return the most relevant results.
- [NL2SQL Tool](https://docs.crewai.com/en/tools/database-data/nl2sqltool.md): The `NL2SQLTool` is designed to convert natural language to SQL queries.
- [Overview](https://docs.crewai.com/en/tools/database-data/overview.md): Connect to databases, vector stores, and data warehouses for comprehensive data access
- [PG RAG Search](https://docs.crewai.com/en/tools/database-data/pgsearchtool.md): The `PGSearchTool` is designed to search PostgreSQL databases and return the most relevant results.
- [Qdrant Vector Search Tool](https://docs.crewai.com/en/tools/database-data/qdrantvectorsearchtool.md): Semantic search capabilities for CrewAI agents using Qdrant vector database
- [Snowflake Search Tool](https://docs.crewai.com/en/tools/database-data/snowflakesearchtool.md): The `SnowflakeSearchTool` enables CrewAI agents to execute SQL queries and perform semantic search on Snowflake data warehouses.
- [Weaviate Vector Search](https://docs.crewai.com/en/tools/database-data/weaviatevectorsearchtool.md): The `WeaviateVectorSearchTool` is designed to search a Weaviate vector database for semantically similar documents.
- [CSV RAG Search](https://docs.crewai.com/en/tools/file-document/csvsearchtool.md): The `CSVSearchTool` is a powerful RAG (Retrieval-Augmented Generation) tool designed for semantic searches within a CSV file's content.
- [Directory Read](https://docs.crewai.com/en/tools/file-document/directoryreadtool.md): The `DirectoryReadTool` is a powerful utility designed to provide a comprehensive listing of directory contents.
- [Directory RAG Search](https://docs.crewai.com/en/tools/file-document/directorysearchtool.md): The `DirectorySearchTool` is a powerful RAG (Retrieval-Augmented Generation) tool designed for semantic searches within a directory's content.
- [DOCX RAG Search](https://docs.crewai.com/en/tools/file-document/docxsearchtool.md): The `DOCXSearchTool` is a RAG tool designed for semantic searching within DOCX documents.
- [File Read](https://docs.crewai.com/en/tools/file-document/filereadtool.md): The `FileReadTool` is designed to read files from the local file system.
- [File Write](https://docs.crewai.com/en/tools/file-document/filewritetool.md): The `FileWriterTool` is designed to write content to files.
- [JSON RAG Search](https://docs.crewai.com/en/tools/file-document/jsonsearchtool.md): The `JSONSearchTool` is designed to search JSON files and return the most relevant results.
- [MDX RAG Search](https://docs.crewai.com/en/tools/file-document/mdxsearchtool.md): The `MDXSearchTool` is designed to search MDX files and return the most relevant results.
- [Overview](https://docs.crewai.com/en/tools/file-document/overview.md): Read, write, and search through various file formats with CrewAI's document processing tools
- [PDF RAG Search](https://docs.crewai.com/en/tools/file-document/pdfsearchtool.md): The `PDFSearchTool` is designed to search PDF files and return the most relevant results.
- [TXT RAG Search](https://docs.crewai.com/en/tools/file-document/txtsearchtool.md): The `TXTSearchTool` is designed to perform a RAG (Retrieval-Augmented Generation) search within the content of a text file.
- [XML RAG Search](https://docs.crewai.com/en/tools/file-document/xmlsearchtool.md): The `XMLSearchTool` is designed to perform a RAG (Retrieval-Augmented Generation) search within the content of a XML file.
- [Tools Overview](https://docs.crewai.com/en/tools/overview.md): Discover CrewAI's extensive library of 40+ tools to supercharge your AI agents
- [Brave Search](https://docs.crewai.com/en/tools/search-research/bravesearchtool.md): The `BraveSearchTool` is designed to search the internet using the Brave Search API.
- [Code Docs RAG Search](https://docs.crewai.com/en/tools/search-research/codedocssearchtool.md): The `CodeDocsSearchTool` is a powerful RAG (Retrieval-Augmented Generation) tool designed for semantic searches within code documentation.
- [EXA Search Web Loader](https://docs.crewai.com/en/tools/search-research/exasearchtool.md): The `EXASearchTool` is designed to perform a semantic search for a specified query from a text's content across the internet.
- [Github Search](https://docs.crewai.com/en/tools/search-research/githubsearchtool.md): The `GithubSearchTool` is designed to search websites and convert them into clean markdown or structured data.
- [Linkup Search Tool](https://docs.crewai.com/en/tools/search-research/linkupsearchtool.md): The `LinkupSearchTool` enables querying the Linkup API for contextual information.
- [Overview](https://docs.crewai.com/en/tools/search-research/overview.md): Perform web searches, find repositories, and research information across the internet
- [Google Serper Search](https://docs.crewai.com/en/tools/search-research/serperdevtool.md): The `SerperDevTool` is designed to search the internet and return the most relevant results.
- [Tavily Extractor Tool](https://docs.crewai.com/en/tools/search-research/tavilyextractortool.md): Extract structured content from web pages using the Tavily API
- [Tavily Search Tool](https://docs.crewai.com/en/tools/search-research/tavilysearchtool.md): Perform comprehensive web searches using the Tavily Search API
- [Website RAG Search](https://docs.crewai.com/en/tools/search-research/websitesearchtool.md): The `WebsiteSearchTool` is designed to perform a RAG (Retrieval-Augmented Generation) search within the content of a website.
- [YouTube Channel RAG Search](https://docs.crewai.com/en/tools/search-research/youtubechannelsearchtool.md): The `YoutubeChannelSearchTool` is designed to perform a RAG (Retrieval-Augmented Generation) search within the content of a Youtube channel.
- [YouTube Video RAG Search](https://docs.crewai.com/en/tools/search-research/youtubevideosearchtool.md): The `YoutubeVideoSearchTool` is designed to perform a RAG (Retrieval-Augmented Generation) search within the content of a Youtube video.
- [Browserbase Web Loader](https://docs.crewai.com/en/tools/web-scraping/browserbaseloadtool.md): Browserbase is a developer platform to reliably run, manage, and monitor headless browsers.
- [Firecrawl Crawl Website](https://docs.crewai.com/en/tools/web-scraping/firecrawlcrawlwebsitetool.md): The `FirecrawlCrawlWebsiteTool` is designed to crawl and convert websites into clean markdown or structured data.
- [Firecrawl Scrape Website](https://docs.crewai.com/en/tools/web-scraping/firecrawlscrapewebsitetool.md): The `FirecrawlScrapeWebsiteTool` is designed to scrape websites and convert them into clean markdown or structured data.
- [Firecrawl Search](https://docs.crewai.com/en/tools/web-scraping/firecrawlsearchtool.md): The `FirecrawlSearchTool` is designed to search websites and convert them into clean markdown or structured data.
- [Hyperbrowser Load Tool](https://docs.crewai.com/en/tools/web-scraping/hyperbrowserloadtool.md): The `HyperbrowserLoadTool` enables web scraping and crawling using Hyperbrowser.
- [Overview](https://docs.crewai.com/en/tools/web-scraping/overview.md): Extract data from websites and automate browser interactions with powerful scraping tools
- [Oxylabs Scrapers](https://docs.crewai.com/en/tools/web-scraping/oxylabsscraperstool.md): Oxylabs Scrapers allow to easily access the information from the respective sources. Please see the list of available sources below:
  - `Amazon Product`
  - `Amazon Search`
  - `Google Seach`
  - `Universal`

- [Scrape Element From Website Tool](https://docs.crewai.com/en/tools/web-scraping/scrapeelementfromwebsitetool.md): The `ScrapeElementFromWebsiteTool` enables CrewAI agents to extract specific elements from websites using CSS selectors.
- [Scrapegraph Scrape Tool](https://docs.crewai.com/en/tools/web-scraping/scrapegraphscrapetool.md): The `ScrapegraphScrapeTool` leverages Scrapegraph AI's SmartScraper API to intelligently extract content from websites.
- [Scrape Website](https://docs.crewai.com/en/tools/web-scraping/scrapewebsitetool.md): The `ScrapeWebsiteTool` is designed to extract and read the content of a specified website.
- [Scrapfly Scrape Website Tool](https://docs.crewai.com/en/tools/web-scraping/scrapflyscrapetool.md): The `ScrapflyScrapeWebsiteTool` leverages Scrapfly's web scraping API to extract content from websites in various formats.
- [Selenium Scraper](https://docs.crewai.com/en/tools/web-scraping/seleniumscrapingtool.md): The `SeleniumScrapingTool` is designed to extract and read the content of a specified website using Selenium.
- [Serper Scrape Website](https://docs.crewai.com/en/tools/web-scraping/serperscrapewebsitetool.md): The `SerperScrapeWebsiteTool` is designed to scrape websites and extract clean, readable content using Serper's scraping API.
- [Spider Scraper](https://docs.crewai.com/en/tools/web-scraping/spidertool.md): The `SpiderTool` is designed to extract and read the content of a specified website using Spider.
- [Stagehand Tool](https://docs.crewai.com/en/tools/web-scraping/stagehandtool.md): Web automation tool that integrates Stagehand with CrewAI for browser interaction and automation
- [Introdução](https://docs.crewai.com/pt-BR/api-reference/introduction.md): Referência completa para a API REST do CrewAI Enterprise
- [Agentes](https://docs.crewai.com/pt-BR/concepts/agents.md): Guia detalhado sobre como criar e gerenciar agentes no framework CrewAI.
- [CLI](https://docs.crewai.com/pt-BR/concepts/cli.md): Aprenda a usar o CLI do CrewAI para interagir com o CrewAI.
- [Colaboração](https://docs.crewai.com/pt-BR/concepts/collaboration.md): Como permitir que agentes trabalhem juntos, deleguem tarefas e se comuniquem de forma eficaz em equipes CrewAI.
- [Crews](https://docs.crewai.com/pt-BR/concepts/crews.md): Compreendendo e utilizando crews no framework crewAI com atributos e funcionalidades abrangentes.
- [Listeners de Evento](https://docs.crewai.com/pt-BR/concepts/event-listener.md): Acesse eventos do CrewAI para criar integrações e monitoramento personalizados
- [Flows](https://docs.crewai.com/pt-BR/concepts/flows.md): Saiba como criar e gerenciar fluxos de trabalho de IA usando CrewAI Flows.
- [Knowledge](https://docs.crewai.com/pt-BR/concepts/knowledge.md): O que é knowledge em CrewAI e como usá-lo.
- [LLMs](https://docs.crewai.com/pt-BR/concepts/llms.md): Um guia abrangente para configurar e usar Modelos de Linguagem de Grande Escala (LLMs) em seus projetos CrewAI
- [Memória](https://docs.crewai.com/pt-BR/concepts/memory.md): Aproveitando sistemas de memória no framework CrewAI para aprimorar as capacidades dos agentes.
- [Planejamento](https://docs.crewai.com/pt-BR/concepts/planning.md): Aprenda como adicionar planejamento à sua CrewAI Crew e melhorar sua performance.
- [Processos](https://docs.crewai.com/pt-BR/concepts/processes.md): Guia detalhado sobre o gerenciamento de fluxos de trabalho através de processos no CrewAI, com detalhes de implementação atualizados.
- [Reasoning](https://docs.crewai.com/pt-BR/concepts/reasoning.md): Aprenda como habilitar e usar o reasoning do agente para aprimorar a execução de tarefas.
- [Tarefas](https://docs.crewai.com/pt-BR/concepts/tasks.md): Guia detalhado sobre como gerenciar e criar tarefas dentro do framework CrewAI.
- [Testes](https://docs.crewai.com/pt-BR/concepts/testing.md): Saiba como testar sua CrewAI Crew e avaliar seu desempenho.
- [Ferramentas](https://docs.crewai.com/pt-BR/concepts/tools.md): Compreendendo e aproveitando ferramentas dentro do framework CrewAI para colaboração e execução de tarefas por agentes.
- [Treinamento](https://docs.crewai.com/pt-BR/concepts/training.md): Aprenda como treinar seus agentes CrewAI fornecendo feedback desde o início e obtenha resultados consistentes.
- [Proteção contra Alucinações](https://docs.crewai.com/pt-BR/enterprise/features/hallucination-guardrail.md): Previna e detecte alucinações de IA nas suas tarefas do CrewAI
- [Integrações](https://docs.crewai.com/pt-BR/enterprise/features/integrations.md): Aplicativos conectados para que seus agentes possam tomar ações.
- [Repositório de Ferramentas](https://docs.crewai.com/pt-BR/enterprise/features/tool-repository.md): Usando o Repositório de Ferramentas para gerenciar suas ferramentas
- [Traces](https://docs.crewai.com/pt-BR/enterprise/features/traces.md): Usando Traces para monitorar seus Crews
- [Webhook Streaming](https://docs.crewai.com/pt-BR/enterprise/features/webhook-streaming.md): Usando Webhook Streaming para transmitir eventos para o seu webhook
- [Configuração do Azure OpenAI](https://docs.crewai.com/pt-BR/enterprise/guides/azure-openai-setup.md): Configure o Azure OpenAI com o Crew Studio para conexões empresariais de LLM
- [Build Crew](https://docs.crewai.com/pt-BR/enterprise/guides/build-crew.md): Uma Crew é um grupo de agentes que trabalham juntos para completar uma tarefa.
- [Deploy Crew](https://docs.crewai.com/pt-BR/enterprise/guides/deploy-crew.md): Implantando um Crew na CrewAI Enterprise
- [Ativar Crew Studio](https://docs.crewai.com/pt-BR/enterprise/guides/enable-crew-studio.md): Ativando o Crew Studio no CrewAI Enterprise
- [Gatilho HubSpot](https://docs.crewai.com/pt-BR/enterprise/guides/hubspot-trigger.md): Acione crews do CrewAI diretamente a partir de Workflows do HubSpot
- [Workflows HITL](https://docs.crewai.com/pt-BR/enterprise/guides/human-in-the-loop.md): Aprenda como implementar workflows Human-In-The-Loop no CrewAI para decisões aprimoradas
- [Kickoff Crew](https://docs.crewai.com/pt-BR/enterprise/guides/kickoff-crew.md): Inicie um Crew no CrewAI Enterprise
- [Exportação de Componentes React](https://docs.crewai.com/pt-BR/enterprise/guides/react-component-export.md): Aprenda como exportar e integrar componentes React do CrewAI Enterprise em suas aplicações
- [Trigger Salesforce](https://docs.crewai.com/pt-BR/enterprise/guides/salesforce-trigger.md): Dispare equipes CrewAI a partir de fluxos de trabalho do Salesforce para automação de CRM
- [Slack Trigger](https://docs.crewai.com/pt-BR/enterprise/guides/slack-trigger.md): Acione crews do CrewAI diretamente do Slack usando comandos de barra
- [Gestão de Equipes](https://docs.crewai.com/pt-BR/enterprise/guides/team-management.md): Aprenda como convidar e gerenciar membros da equipe em sua organização CrewAI Enterprise
- [Atualizar Crew](https://docs.crewai.com/pt-BR/enterprise/guides/update-crew.md): Atualizando uma Crew no CrewAI Enterprise
- [Automação com Webhook](https://docs.crewai.com/pt-BR/enterprise/guides/webhook-automation.md): Automatize fluxos de trabalho do CrewAI Enterprise usando webhooks com plataformas como ActivePieces, Zapier e Make.com
- [Trigger Zapier](https://docs.crewai.com/pt-BR/enterprise/guides/zapier-trigger.md): Dispare crews do CrewAI a partir de fluxos de trabalho no Zapier para automatizar fluxos multiaplicativos
- [Integração com Asana](https://docs.crewai.com/pt-BR/enterprise/integrations/asana.md): Coordenação de tarefas e projetos em equipe com a integração Asana para CrewAI.
- [Integração com Box](https://docs.crewai.com/pt-BR/enterprise/integrations/box.md): Armazenamento de arquivos e gerenciamento de documentos com a integração do Box para CrewAI.
- [Integração com ClickUp](https://docs.crewai.com/pt-BR/enterprise/integrations/clickup.md): Gerenciamento de tarefas e produtividade com integração ClickUp para CrewAI.
- [Integração com GitHub](https://docs.crewai.com/pt-BR/enterprise/integrations/github.md): Gerenciamento de repositórios e issues com a integração do GitHub para CrewAI.
- [Integração com Gmail](https://docs.crewai.com/pt-BR/enterprise/integrations/gmail.md): Gerenciamento de e-mails e contatos com a integração do Gmail para o CrewAI.
- [Integração com Google Calendar](https://docs.crewai.com/pt-BR/enterprise/integrations/google_calendar.md): Gerenciamento de eventos e agendas com integração ao Google Calendar para o CrewAI.
- [Integração com Google Sheets](https://docs.crewai.com/pt-BR/enterprise/integrations/google_sheets.md): Sincronização de dados de planilhas com a integração do Google Sheets para CrewAI.
- [Integração com HubSpot](https://docs.crewai.com/pt-BR/enterprise/integrations/hubspot.md): Gerencie empresas e contatos no HubSpot com o CrewAI.
- [Integração com Jira](https://docs.crewai.com/pt-BR/enterprise/integrations/jira.md): Rastreamento de problemas e gestão de projetos com a integração Jira para CrewAI.
- [Integração com o Linear](https://docs.crewai.com/pt-BR/enterprise/integrations/linear.md): Acompanhamento de projetos de software e rastreamento de bugs com a integração Linear para CrewAI.
- [Integração com o Notion](https://docs.crewai.com/pt-BR/enterprise/integrations/notion.md): Gerenciamento de páginas e bancos de dados com integração do Notion para o CrewAI.
- [Integração com Salesforce](https://docs.crewai.com/pt-BR/enterprise/integrations/salesforce.md): Automação de vendas e CRM com integração Salesforce para CrewAI.
- [Integração com Shopify](https://docs.crewai.com/pt-BR/enterprise/integrations/shopify.md): Gestão de e-commerce e loja online com integração do Shopify para CrewAI.
- [Integração com Slack](https://docs.crewai.com/pt-BR/enterprise/integrations/slack.md): Comunicação e colaboração em equipe com a integração Slack para CrewAI.
- [Integração Stripe](https://docs.crewai.com/pt-BR/enterprise/integrations/stripe.md): Processamento de pagamentos e gerenciamento de assinaturas com integração Stripe para CrewAI.
- [Integração com Zendesk](https://docs.crewai.com/pt-BR/enterprise/integrations/zendesk.md): Gestão de suporte ao cliente e helpdesk com integração Zendesk para CrewAI.
- [CrewAI Enterprise](https://docs.crewai.com/pt-BR/enterprise/introduction.md): Implemente, monitore e escale seus fluxos de trabalho de agentes de IA
- [FAQs](https://docs.crewai.com/pt-BR/enterprise/resources/frequently-asked-questions.md): Perguntas frequentes sobre CrewAI Enterprise
- [Exemplos CrewAI](https://docs.crewai.com/pt-BR/examples/example.md): Uma coleção de exemplos que mostram como usar o framework CrewAI para automatizar fluxos de trabalho.
- [Personalizando Prompts](https://docs.crewai.com/pt-BR/guides/advanced/customizing-prompts.md): Aprofunde-se na personalização de prompts de baixo nível no CrewAI, habilitando casos de uso super customizados e complexos para diferentes modelos e idiomas.
- [Impressão digital](https://docs.crewai.com/pt-BR/guides/advanced/fingerprinting.md): Saiba como usar o sistema de impressão digital da CrewAI para identificar e rastrear componentes de forma única durante todo o seu ciclo de vida.
- [Criando Agentes Eficazes](https://docs.crewai.com/pt-BR/guides/agents/crafting-effective-agents.md): Aprenda as melhores práticas para projetar agentes de IA poderosos e especializados que colaboram de forma eficaz para resolver problemas complexos.
- [Avaliando Casos de Uso para CrewAI](https://docs.crewai.com/pt-BR/guides/concepts/evaluating-use-cases.md): Aprenda a avaliar as necessidades da sua aplicação de IA e escolher a abordagem certa entre Crews e Flows com base nos requisitos de complexidade e precisão.
- [Monte sua Primeira Crew](https://docs.crewai.com/pt-BR/guides/crews/first-crew.md): Tutorial passo a passo para criar uma equipe colaborativa de IA que trabalha junta para resolver problemas complexos.
- [Construa Seu Primeiro Flow](https://docs.crewai.com/pt-BR/guides/flows/first-flow.md): Aprenda como criar fluxos de trabalho estruturados e orientados a eventos com controle preciso sobre a execução.
- [Dominando o Gerenciamento de Estado em Flows](https://docs.crewai.com/pt-BR/guides/flows/mastering-flow-state.md): Um guia abrangente sobre como gerenciar, persistir e utilizar o estado em CrewAI Flows para construir aplicações de IA robustas.
- [Instalação](https://docs.crewai.com/pt-BR/installation.md): Comece a usar o CrewAI - Instale, configure e crie seu primeiro crew de IA
- [Introdução](https://docs.crewai.com/pt-BR/introduction.md): Construa equipes de agentes de IA que trabalham juntas para resolver tarefas complexas
- [Hooks Antes e Depois do Kickoff](https://docs.crewai.com/pt-BR/learn/before-and-after-kickoff-hooks.md): Aprenda a usar hooks antes e depois do kickoff em CrewAI
- [Traga seu próprio agente](https://docs.crewai.com/pt-BR/learn/bring-your-own-agent.md): Aprenda como trazer seus próprios agentes que funcionam dentro de uma Crew.
- [Agentes de Codificação](https://docs.crewai.com/pt-BR/learn/coding-agents.md): Aprenda como habilitar seus Agentes CrewAI para escrever e executar código, e explore funcionalidades avançadas para maior potencial.
- [Tarefas Condicionais](https://docs.crewai.com/pt-BR/learn/conditional-tasks.md): Saiba como usar tarefas condicionais em um kickoff do crewAI
- [Criar Ferramentas Personalizadas](https://docs.crewai.com/pt-BR/learn/create-custom-tools.md): Guia abrangente sobre como criar, utilizar e gerenciar ferramentas personalizadas dentro do framework CrewAI, incluindo novas funcionalidades e tratamento de erros.
- [Implementação de LLM Personalizada](https://docs.crewai.com/pt-BR/learn/custom-llm.md): Aprenda a criar implementações personalizadas de LLM no CrewAI.
- [Agente Gerente Personalizado](https://docs.crewai.com/pt-BR/learn/custom-manager-agent.md): Saiba como definir um agente personalizado como gerente no CrewAI, proporcionando mais controle sobre o gerenciamento e a coordenação das tarefas.
- [Personalize Agentes](https://docs.crewai.com/pt-BR/learn/customizing-agents.md): Um guia abrangente para adaptar agentes a funções específicas, tarefas e customizações avançadas dentro do framework CrewAI.
- [Geração de Imagens com DALL-E](https://docs.crewai.com/pt-BR/learn/dalle-image-generation.md): Aprenda a usar o DALL-E para geração de imagens com IA em seus projetos CrewAI
- [Forçar a Saída da Ferramenta como Resultado](https://docs.crewai.com/pt-BR/learn/force-tool-output-as-result.md): Aprenda como forçar a saída de uma ferramenta como resultado em uma tarefa de Agent no CrewAI.
- [Processo Hierárquico](https://docs.crewai.com/pt-BR/learn/hierarchical-process.md): Um guia abrangente para compreender e aplicar o processo hierárquico em seus projetos CrewAI, atualizado para refletir as práticas de codificação e funcionalidades mais recentes.
- [Workflows Human-in-the-Loop (HITL)](https://docs.crewai.com/pt-BR/learn/human-in-the-loop.md): Aprenda como implementar workflows Human-in-the-Loop na CrewAI para aprimorar a tomada de decisões
- [Input Humano na Execução](https://docs.crewai.com/pt-BR/learn/human-input-on-execution.md): Integrando o CrewAI com input humano durante a execução em processos complexos de tomada de decisão e aproveitando ao máximo todos os atributos e ferramentas do agente.
- [Inicie uma Crew de Forma Assíncrona](https://docs.crewai.com/pt-BR/learn/kickoff-async.md): Inicie uma Crew de Forma Assíncrona
- [Kickoff Crew para Cada](https://docs.crewai.com/pt-BR/learn/kickoff-for-each.md): Kickoff Crew para Cada Item em uma Lista
- [Conecte-se a qualquer LLM](https://docs.crewai.com/pt-BR/learn/llm-connections.md): Guia abrangente sobre como integrar o CrewAI a diversos Large Language Models (LLMs) usando o LiteLLM, incluindo provedores compatíveis e opções de configuração.
- [Guia Estratégico de Seleção de LLMs](https://docs.crewai.com/pt-BR/learn/llm-selection-guide.md): Framework estratégico para escolher o LLM certo para seus agentes CrewAI e escrever definições eficazes de tarefas e agentes
- [Usando Agentes Multimodais](https://docs.crewai.com/pt-BR/learn/multimodal-agents.md): Aprenda como habilitar e usar capacidades multimodais em seus agentes para processar imagens e outros conteúdos não textuais dentro do framework CrewAI.
- [Visão Geral](https://docs.crewai.com/pt-BR/learn/overview.md): Aprenda como construir, personalizar e otimizar suas aplicações CrewAI com guias e tutoriais completos
- [Reexecutar Tarefas a partir do Último Crew Kickoff](https://docs.crewai.com/pt-BR/learn/replay-tasks-from-latest-crew-kickoff.md): Reexecute tarefas a partir do último crew.kickoff(...)
- [Processos Sequenciais](https://docs.crewai.com/pt-BR/learn/sequential-process.md): Um guia abrangente para utilizar os processos sequenciais na execução de tarefas em projetos CrewAI.
- [Usando Anotações no crew.py](https://docs.crewai.com/pt-BR/learn/using-annotations.md): Aprenda como usar anotações para estruturar corretamente agentes, tarefas e componentes no CrewAI
- [Conectando a Múltiplos Servidores MCP](https://docs.crewai.com/pt-BR/mcp/multiple-servers.md): Saiba como usar o MCPServerAdapter no CrewAI para conectar-se simultaneamente a múltiplos servidores MCP e agregar suas ferramentas.
- [Servidores MCP como Ferramentas no CrewAI](https://docs.crewai.com/pt-BR/mcp/overview.md): Aprenda como integrar servidores MCP como ferramentas nos seus agentes CrewAI usando a biblioteca `crewai-tools`.
- [Considerações de Segurança MCP](https://docs.crewai.com/pt-BR/mcp/security.md): Saiba mais sobre as principais melhores práticas de segurança ao integrar servidores MCP com seus agentes CrewAI.
- [Transporte SSE](https://docs.crewai.com/pt-BR/mcp/sse.md): Saiba como conectar o CrewAI a servidores MCP remotos usando Server-Sent Events (SSE) para comunicação em tempo real.
- [Transporte Stdio](https://docs.crewai.com/pt-BR/mcp/stdio.md): Aprenda como conectar o CrewAI a servidores MCP locais usando o mecanismo de transporte Stdio (Entrada/Saída Padrão).
- [Transporte HTTP Streamable](https://docs.crewai.com/pt-BR/mcp/streamable-http.md): Saiba como conectar o CrewAI a servidores MCP remotos usando o transporte HTTP Streamable flexível.
- [Integração com AgentOps](https://docs.crewai.com/pt-BR/observability/agentops.md): Entendendo e registrando a performance do seu agente com AgentOps.
- [Arize Phoenix](https://docs.crewai.com/pt-BR/observability/arize-phoenix.md): Integração do Arize Phoenix para CrewAI com OpenTelemetry e OpenInference
- [Integração Langfuse](https://docs.crewai.com/pt-BR/observability/langfuse.md): Saiba como integrar o Langfuse ao CrewAI via OpenTelemetry usando OpenLit
- [Integração com Langtrace](https://docs.crewai.com/pt-BR/observability/langtrace.md): Como monitorar custo, latência e desempenho dos Agentes CrewAI usando o Langtrace, uma ferramenta externa de observabilidade.
- [Integração Maxim](https://docs.crewai.com/pt-BR/observability/maxim.md): Inicie o monitoramento, avaliação e observabilidade de agentes
- [Integração com MLflow](https://docs.crewai.com/pt-BR/observability/mlflow.md): Comece rapidamente a monitorar seus Agents com MLflow.
- [Integração OpenLIT](https://docs.crewai.com/pt-BR/observability/openlit.md): Comece a monitorar seus Agentes rapidamente com apenas uma linha de código usando OpenTelemetry.
- [Integração Opik](https://docs.crewai.com/pt-BR/observability/opik.md): Saiba como usar o Comet Opik para depurar, avaliar e monitorar suas aplicações CrewAI com rastreamento abrangente, avaliações automatizadas e dashboards prontos para produção.
- [Visão Geral](https://docs.crewai.com/pt-BR/observability/overview.md): Monitore, avalie e otimize seus agentes CrewAI com ferramentas de observabilidade abrangentes
- [Avaliação Patronus AI](https://docs.crewai.com/pt-BR/observability/patronus-evaluation.md): Monitore e avalie o desempenho de agentes CrewAI utilizando a plataforma abrangente de avaliação da Patronus AI para saídas de LLM e comportamentos de agentes.
- [Integração com Portkey](https://docs.crewai.com/pt-BR/observability/portkey.md): Como usar Portkey com CrewAI
- [Integração com Weave](https://docs.crewai.com/pt-BR/observability/weave.md): Saiba como usar o Weights & Biases (W&B) Weave para rastrear, experimentar, avaliar e melhorar suas aplicações CrewAI.
- [Guia Rápido](https://docs.crewai.com/pt-BR/quickstart.md): Construa seu primeiro agente de IA com a CrewAI em menos de 5 minutos.
- [null](https://docs.crewai.com/pt-BR/snippets/snippet-intro.md)
- [Telemetria](https://docs.crewai.com/pt-BR/telemetry.md): Entendendo os dados de telemetria coletados pelo CrewAI e como eles contribuem para o aprimoramento da biblioteca.
- [AI Mind Tool](https://docs.crewai.com/pt-BR/tools/ai-ml/aimindtool.md): O `AIMindTool` foi desenvolvido para consultar fontes de dados em linguagem natural.
- [Interpretador de Código](https://docs.crewai.com/pt-BR/tools/ai-ml/codeinterpretertool.md): O `CodeInterpreterTool` é uma poderosa ferramenta projetada para executar código Python 3 em um ambiente seguro e isolado.
- [Ferramenta DALL-E](https://docs.crewai.com/pt-BR/tools/ai-ml/dalletool.md): A `DallETool` é uma ferramenta poderosa projetada para gerar imagens a partir de descrições textuais.
- [Ferramenta LangChain](https://docs.crewai.com/pt-BR/tools/ai-ml/langchaintool.md): O `LangChainTool` é um wrapper para ferramentas LangChain e mecanismos de consulta.
- [Ferramenta LlamaIndex](https://docs.crewai.com/pt-BR/tools/ai-ml/llamaindextool.md): A `LlamaIndexTool` é um wrapper para ferramentas e mecanismos de consulta do LlamaIndex.
- [Visão Geral](https://docs.crewai.com/pt-BR/tools/ai-ml/overview.md): Aproveite serviços de IA, gere imagens, processe visão e construa sistemas inteligentes
- [Ferramenta RAG](https://docs.crewai.com/pt-BR/tools/ai-ml/ragtool.md): O `RagTool` é uma ferramenta dinâmica de base de conhecimento para responder perguntas usando Geração Aumentada por Recuperação.
- [Vision Tool](https://docs.crewai.com/pt-BR/tools/ai-ml/visiontool.md): O `VisionTool` foi projetado para extrair texto de imagens.
- [Apify Actors](https://docs.crewai.com/pt-BR/tools/automation/apifyactorstool.md): `ApifyActorsTool` permite que você execute Apify Actors para adicionar recursos de raspagem de dados na web, coleta, extração de dados e automação web aos seus fluxos de trabalho CrewAI.
- [Ferramenta Composio](https://docs.crewai.com/pt-BR/tools/automation/composiotool.md): O Composio oferece mais de 250 ferramentas prontas para produção para agentes de IA com gerenciamento de autenticação flexível.
- [MultiOn Tool](https://docs.crewai.com/pt-BR/tools/automation/multiontool.md): O `MultiOnTool` permite que agentes CrewAI naveguem e interajam com a web por meio de instruções em linguagem natural.
- [Visão Geral](https://docs.crewai.com/pt-BR/tools/automation/overview.md): Automatize fluxos de trabalho e integre com plataformas e serviços externos
- [Ferramenta Bedrock Invoke Agent](https://docs.crewai.com/pt-BR/tools/cloud-storage/bedrockinvokeagenttool.md): Permite que agentes CrewAI invoquem Amazon Bedrock Agents e aproveitem suas capacidades em seus fluxos de trabalho
- [Bedrock Knowledge Base Retriever](https://docs.crewai.com/pt-BR/tools/cloud-storage/bedrockkbretriever.md): Recupere informações das Bases de Conhecimento do Amazon Bedrock usando consultas em linguagem natural
- [Visão Geral](https://docs.crewai.com/pt-BR/tools/cloud-storage/overview.md): Interaja com serviços em nuvem, sistemas de armazenamento e plataformas de IA baseadas em nuvem
- [S3 Reader Tool](https://docs.crewai.com/pt-BR/tools/cloud-storage/s3readertool.md): O `S3ReaderTool` permite que agentes CrewAI leiam arquivos de buckets Amazon S3.
- [Ferramenta S3 Writer](https://docs.crewai.com/pt-BR/tools/cloud-storage/s3writertool.md): A `S3WriterTool` permite que agentes CrewAI escrevam conteúdo em arquivos em buckets Amazon S3.
- [Busca RAG no MySQL](https://docs.crewai.com/pt-BR/tools/database-data/mysqltool.md): O `MySQLSearchTool` foi projetado para buscar em bancos de dados MySQL e retornar os resultados mais relevantes.
- [NL2SQL Tool](https://docs.crewai.com/pt-BR/tools/database-data/nl2sqltool.md): O `NL2SQLTool` foi projetado para converter linguagem natural em consultas SQL.
- [Visão Geral](https://docs.crewai.com/pt-BR/tools/database-data/overview.md): Conecte-se a bancos de dados, armazenamentos vetoriais e data warehouses para acesso abrangente aos dados
- [PG RAG Search](https://docs.crewai.com/pt-BR/tools/database-data/pgsearchtool.md): O `PGSearchTool` foi desenvolvido para pesquisar bancos de dados PostgreSQL e retornar os resultados mais relevantes.
- [Qdrant Vector Search Tool](https://docs.crewai.com/pt-BR/tools/database-data/qdrantvectorsearchtool.md): Capacidades de busca semântica para agentes CrewAI usando o banco de dados vetorial Qdrant
- [Snowflake Search Tool](https://docs.crewai.com/pt-BR/tools/database-data/snowflakesearchtool.md): O `SnowflakeSearchTool` permite que agentes CrewAI executem consultas SQL e realizem buscas semânticas em data warehouses Snowflake.
- [Busca Vetorial Weaviate](https://docs.crewai.com/pt-BR/tools/database-data/weaviatevectorsearchtool.md): O `WeaviateVectorSearchTool` foi projetado para buscar documentos semanticamente similares em um banco de dados vetorial Weaviate.
- [Busca RAG em CSV](https://docs.crewai.com/pt-BR/tools/file-document/csvsearchtool.md): O `CSVSearchTool` é uma poderosa ferramenta RAG (Geração com Recuperação Aprimorada) projetada para buscas semânticas no conteúdo de arquivos CSV.
- [Leitura de Diretório](https://docs.crewai.com/pt-BR/tools/file-document/directoryreadtool.md): O `DirectoryReadTool` é uma poderosa utilidade projetada para fornecer uma listagem abrangente do conteúdo de diretórios.
- [Busca RAG em Diretório](https://docs.crewai.com/pt-BR/tools/file-document/directorysearchtool.md): O `DirectorySearchTool` é uma poderosa ferramenta RAG (Retrieval-Augmented Generation) desenvolvida para buscas semânticas no conteúdo de um diretório.
- [Pesquisa RAG em DOCX](https://docs.crewai.com/pt-BR/tools/file-document/docxsearchtool.md): A `DOCXSearchTool` é uma ferramenta RAG projetada para busca semântica em documentos DOCX.
- [Leitura de Arquivo](https://docs.crewai.com/pt-BR/tools/file-document/filereadtool.md): O `FileReadTool` foi desenvolvido para ler arquivos do sistema de arquivos local.
- [Escrita de Arquivo](https://docs.crewai.com/pt-BR/tools/file-document/filewritetool.md): O `FileWriterTool` foi projetado para escrever conteúdo em arquivos.
- [Busca JSON RAG](https://docs.crewai.com/pt-BR/tools/file-document/jsonsearchtool.md): O `JSONSearchTool` foi projetado para buscar arquivos JSON e retornar os resultados mais relevantes.
- [Pesquisa MDX RAG](https://docs.crewai.com/pt-BR/tools/file-document/mdxsearchtool.md): O `MDXSearchTool` foi projetado para pesquisar arquivos MDX e retornar os resultados mais relevantes.
- [Visão Geral](https://docs.crewai.com/pt-BR/tools/file-document/overview.md): Leia, escreva e pesquise em diversos formatos de arquivos com as ferramentas de processamento de documentos do CrewAI
- [Busca RAG em PDF](https://docs.crewai.com/pt-BR/tools/file-document/pdfsearchtool.md): O `PDFSearchTool` é projetado para pesquisar arquivos PDF e retornar os resultados mais relevantes.
- [Pesquisa TXT RAG](https://docs.crewai.com/pt-BR/tools/file-document/txtsearchtool.md): O `TXTSearchTool` foi projetado para realizar uma busca RAG (Geração Aumentada por Recuperação) dentro do conteúdo de um arquivo de texto.
- [Busca RAG em XML](https://docs.crewai.com/pt-BR/tools/file-document/xmlsearchtool.md): O `XMLSearchTool` foi projetado para realizar uma busca RAG (Geração Aumentada por Recuperação) dentro do conteúdo de um arquivo XML.
- [Visão Geral das Ferramentas](https://docs.crewai.com/pt-BR/tools/overview.md): Descubra a vasta biblioteca do CrewAI com mais de 40 ferramentas para potencializar seus agentes de IA
- [Brave Search](https://docs.crewai.com/pt-BR/tools/search-research/bravesearchtool.md): O `BraveSearchTool` foi projetado para pesquisar na internet usando a Brave Search API.
- [Pesquisa com RAG em Documentação de Código](https://docs.crewai.com/pt-BR/tools/search-research/codedocssearchtool.md): O `CodeDocsSearchTool` é uma poderosa ferramenta RAG (Geração Aumentada por Recuperação) projetada para buscas semânticas em documentação de código.
- [Carregador Web EXA Search](https://docs.crewai.com/pt-BR/tools/search-research/exasearchtool.md): O `EXASearchTool` foi projetado para realizar uma busca semântica para uma consulta especificada a partir do conteúdo de um texto em toda a internet.
- [Github Search](https://docs.crewai.com/pt-BR/tools/search-research/githubsearchtool.md): O `GithubSearchTool` foi desenvolvido para pesquisar sites e convertê-los em markdown limpo ou dados estruturados.
- [Linkup Search Tool](https://docs.crewai.com/pt-BR/tools/search-research/linkupsearchtool.md): O `LinkupSearchTool` permite consultar a API do Linkup para obter informações contextuais.
- [Visão Geral](https://docs.crewai.com/pt-BR/tools/search-research/overview.md): Realize pesquisas na web, encontre repositórios e pesquise informações em toda a internet
- [Pesquisa Serper Google](https://docs.crewai.com/pt-BR/tools/search-research/serperdevtool.md): O `SerperDevTool` é projetado para pesquisar na internet e retornar os resultados mais relevantes.
- [Pesquisa RAG em Sites](https://docs.crewai.com/pt-BR/tools/search-research/websitesearchtool.md): O `WebsiteSearchTool` foi projetado para realizar uma busca RAG (Geração Aumentada por Recuperação) dentro do conteúdo de um site.
- [Busca RAG em Canal do YouTube](https://docs.crewai.com/pt-BR/tools/search-research/youtubechannelsearchtool.md): O `YoutubeChannelSearchTool` foi desenvolvido para realizar buscas RAG (Retrieval-Augmented Generation) no conteúdo de um canal do Youtube.
- [Pesquisa RAG em Vídeos do YouTube](https://docs.crewai.com/pt-BR/tools/search-research/youtubevideosearchtool.md): O `YoutubeVideoSearchTool` foi projetado para realizar uma busca RAG (Geração Auxiliada por Recuperação) no conteúdo de um vídeo do Youtube.
- [Carregador Web Browserbase](https://docs.crewai.com/pt-BR/tools/web-scraping/browserbaseloadtool.md): O Browserbase é uma plataforma para desenvolvedores para executar, gerenciar e monitorar navegadores headless de forma confiável.
- [Firecrawl Crawl Website](https://docs.crewai.com/pt-BR/tools/web-scraping/firecrawlcrawlwebsitetool.md): O `FirecrawlCrawlWebsiteTool` foi projetado para rastrear e converter sites em markdown limpo ou dados estruturados.
- [Firecrawl Scrape Website](https://docs.crewai.com/pt-BR/tools/web-scraping/firecrawlscrapewebsitetool.md): A ferramenta `FirecrawlScrapeWebsiteTool` foi projetada para fazer scraping de sites e convertê-los em markdown limpo ou dados estruturados.
- [Firecrawl Search](https://docs.crewai.com/pt-BR/tools/web-scraping/firecrawlsearchtool.md): O `FirecrawlSearchTool` foi projetado para pesquisar sites e convertê-los em markdown limpo ou dados estruturados.
- [Hyperbrowser Load Tool](https://docs.crewai.com/pt-BR/tools/web-scraping/hyperbrowserloadtool.md): O `HyperbrowserLoadTool` permite realizar web scraping e crawling utilizando o Hyperbrowser.
- [Visão Geral](https://docs.crewai.com/pt-BR/tools/web-scraping/overview.md): Extraia dados de websites e automatize interações com o navegador utilizando poderosas ferramentas de scraping
- [Oxylabs Scrapers](https://docs.crewai.com/pt-BR/tools/web-scraping/oxylabsscraperstool.md): Os Scrapers da Oxylabs permitem acessar facilmente informações de fontes específicas. Veja abaixo a lista de fontes disponíveis:
  - `Amazon Product`
  - `Amazon Search`
  - `Google Seach`
  - `Universal`

- [Ferramenta de Extração de Elementos de Website](https://docs.crewai.com/pt-BR/tools/web-scraping/scrapeelementfromwebsitetool.md): A `ScrapeElementFromWebsiteTool` permite que agentes CrewAI extraiam elementos específicos de websites usando seletores CSS.
- [Ferramenta de Extração Scrapegraph](https://docs.crewai.com/pt-BR/tools/web-scraping/scrapegraphscrapetool.md): A `ScrapegraphScrapeTool` utiliza a API SmartScraper da Scrapegraph AI para extrair conteúdo de sites de forma inteligente.
- [Raspar Site](https://docs.crewai.com/pt-BR/tools/web-scraping/scrapewebsitetool.md): O `ScrapeWebsiteTool` foi desenvolvido para extrair e ler o conteúdo de um site especificado.
- [Ferramenta de Raspagem de Sites Scrapfly](https://docs.crewai.com/pt-BR/tools/web-scraping/scrapflyscrapetool.md): A `ScrapflyScrapeWebsiteTool` aproveita a API de web scraping da Scrapfly para extrair conteúdo de sites em diversos formatos.
- [Selenium Scraper](https://docs.crewai.com/pt-BR/tools/web-scraping/seleniumscrapingtool.md): O `SeleniumScrapingTool` foi desenvolvido para extrair e ler o conteúdo de um site específico utilizando o Selenium.
- [Spider Scraper](https://docs.crewai.com/pt-BR/tools/web-scraping/spidertool.md): O `SpiderTool` foi projetado para extrair e ler o conteúdo de um site especificado usando o Spider.
- [Ferramenta Stagehand](https://docs.crewai.com/pt-BR/tools/web-scraping/stagehandtool.md): Ferramenta de automação web que integra o Stagehand ao CrewAI para interação e automação em navegadores


## Optional

- [Website](https://crewai.com)
- [Forum](https://community.crewai.com)
- [Crew GPT](https://chatgpt.com/g/g-qqTuUWsBY-crewai-assistant)
- [Releases](https://github.com/crewAIInc/crewAI/releases)
- [Website](https://crewai.com)
- [Fórum](https://community.crewai.com)
- [Crew GPT](https://chatgpt.com/g/g-qqTuUWsBY-crewai-assistant)
- [Lançamentos](https://github.com/crewAIInc/crewAI/releases)
解释