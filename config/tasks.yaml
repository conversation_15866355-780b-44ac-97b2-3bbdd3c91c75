# ===================================================================
#  优化后的任务配置 - 灵活且专业的诊断流程
# ===================================================================

k8s_diagnostic_test_task:
  description: |
    作为 Kubernetes 诊断专家，对指定命名空间进行全面的集群健康检查和问题诊断。

    **诊断范围:** 命名空间 '{namespace}'
    {%- if pod_name %}
    **重点关注:** Pod '{pod_name}'
    {%- endif %}

    **执行标准诊断流程:**

    1. **集群概览**
       - 检查当前 kubectl 上下文和集群连接状态
       - 获取命名空间中所有资源的概览

    2. **Pod 状态分析**
       - 列出所有 Pod 及其状态
       - 识别异常状态：Failed、Pending、CrashLoopBackOff、Evicted
       - 分析重启次数和运行时长模式

    3. **问题 Pod 深度诊断**
       - 对每个异常 Pod 执行 describe 获取详细信息
       - 分析事件历史，查找错误模式
       - 检查资源配置：requests、limits、QoS 类别
       - 获取容器日志，查找错误信息

    4. **节点和调度分析**
       - 检查 Pod 所在节点的资源使用情况
       - 分析调度约束：节点选择器、污点、亲和性
       - 评估资源竞争和调度压力

    5. **配置和最佳实践检查**
       - 验证资源配置的合理性
       - 检查健康检查配置
       - 评估安全上下文设置

    **输出要求:**
    - 结构化的诊断报告
    - 明确的问题根因分析
    - 具体的修复建议和配置示例
    - 预防措施和最佳实践建议

  expected_output: |
    生成专业的 Kubernetes 诊断报告，包含：

    ## 🔍 集群诊断报告

    ### 📊 概览信息
    - 集群上下文和连接状态
    - 命名空间资源统计
    - 整体健康状况评估

    ### 🚨 发现的问题
    - 问题 Pod 清单及状态分析
    - 错误事件和日志摘要
    - 资源配置问题识别

    ### 🔧 根因分析
    - 每个问题的详细分析
    - 相关配置和环境因素
    - 问题影响范围评估

    ### 💡 解决方案
    - 具体的修复步骤
    - 配置调整建议
    - 验证方法

    ### 🛡️ 预防建议
    - 最佳实践建议
    - 监控和告警配置
    - 长期优化方向

sls_log_analysis_task:
  description: >
    分析 SLS 项目 '{sls_project}' 中日志库 '{sls_logstore}' 的日志，查找与
    命名空间 '{namespace}' 相关的 OOM 事件，时间范围：过去 {time_range}。

    分析策略：
    1. **关键词搜索**: 使用 sls_translate_text_to_sql_query 构建查询，
       搜索 OOM 相关关键词
    2. **时间序列分析**: 分析 OOM 事件的发生时间和频率模式
    3. **上下文分析**: 获取 OOM 事件前后的相关日志，识别触发因素
    4. **异常检测**: 识别异常的内存使用模式和突发事件

    关注要点：
    - OOMKiller 调用记录
    - 内存分配失败日志
    - 应用层内存异常
    - 系统资源竞争日志
  expected_output: >
    SLS 日志分析报告，包含：
    1. OOM 事件时间线和频率统计
    2. 关键错误日志摘要和分析
    3. 异常模式识别结果
    4. 可能的触发因素分析

arms_metric_analysis_task:
  description: >
    分析 ARMS 项目 '{arms_project}' 在区域 '{regionId}' 的监控数据，重点
    关注命名空间 '{namespace}' 的内存使用情况，时间范围：过去 {time_range}。

    分析维度：
    1. **内存使用趋势**: 使用 cms_execute_promql_query 查询
       container_memory_usage_bytes
    2. **资源配置对比**: 对比实际使用与 limit/request 配置
    3. **节点级分析**: 分析节点整体内存压力和可用性
    4. **异常检测**: 识别内存使用的异常峰值和增长趋势

    关键指标：
    - container_memory_usage_bytes
    - container_memory_rss
    - container_memory_max_usage_bytes
    - node_memory_MemAvailable_bytes
    - container_memory_failures_total
  expected_output: >
    ARMS 监控分析报告，包含：
    1. 内存使用趋势图表和统计数据
    2. 资源配置合理性评估
    3. 节点资源压力分析
    4. 容量规划建议

analysis_propose_task:
  description: >
    作为首席 SRE 专家，综合分析来自 K8s、SLS、ARMS 三个维度的诊断数据，
    按照专业的故障处理流程，确定根本原因并制定解决方案。

    分析框架：
    1. **数据整合**: 整合所有诊断信息，构建完整的问题画像
    2. **根因分析**: 基于证据链，运用5-Why分析法确定根本原因
    3. **影响评估**: 评估问题的影响范围和紧急程度
    4. **方案制定**: 制定短期修复和长期优化方案

    决策原则：
    - 数据驱动，避免主观臆断
    - 优先解决根本原因，而非症状
    - 考虑方案的可行性和风险
    - 制定可量化的成功标准

    如果需要执行变更操作，必须：
    - 明确操作步骤和参数
    - 评估操作风险和影响范围
    - 制定回滚方案
    - 获得用户确认后再执行
  expected_output: >
    专业的故障分析和解决方案，包含：
    1. 问题根因分析和证据链
    2. 影响范围和严重程度评估
    3. 详细的解决方案和操作步骤
    4. 风险评估和回滚方案
    5. 长期优化建议

report_generation_task:
  description: >
    基于整个诊断和处理过程，生成专业的故障处理报告，为团队知识积累和
    流程改进提供参考。

    报告结构：
    1. **执行摘要**: 问题概述、解决方案、关键结论
    2. **问题描述**: 故障现象、影响范围、发现时间
    3. **诊断过程**: 详细的分析步骤和发现
    4. **根因分析**: 技术根因、流程根因、深层原因
    5. **解决方案**: 具体操作、验证结果、效果评估
    6. **经验总结**: 经验教训、最佳实践、改进建议
    7. **预防措施**: 监控增强、流程优化、培训需求

    报告特点：
    - 结构清晰，逻辑严密
    - 数据详实，结论可信
    - 既有技术深度，又有管理视角
    - 具有很强的参考价值和指导意义
  expected_output: >
    完整的故障处理报告，包含：
    1. 结构化的故障分析和处理记录
    2. 可操作的改进建议和预防措施
    3. 团队学习和知识沉淀内容
    4. 流程优化和工具改进建议
