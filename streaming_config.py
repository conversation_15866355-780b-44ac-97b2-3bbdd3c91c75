# 流式输出配置文件

# 输出格式配置
OUTPUT_CONFIG = {
    # 是否显示时间戳
    "show_timestamps": True,

    # 是否显示详细的工具参数
    "show_tool_args": True,

    # 工具输出最大长度
    "max_tool_output_length": 300,

    # 任务描述最大长度
    "max_task_desc_length": 120,

    # 敏感参数过滤关键词
    "sensitive_keywords": ["password", "token", "key", "secret", "auth"],

    # 是否启用彩色输出（需要colorama库）
    "enable_colors": False,

    # 流式输出缓冲区大小（字符数）
    "stream_buffer_size": 1024,
}

# 事件过滤配置
EVENT_FILTER = {
    # 要监听的事件类型
    "enabled_events": [
        "CrewKickoffStartedEvent",
        "CrewKickoffCompletedEvent",
        "AgentExecutionStartedEvent",
        "AgentExecutionCompletedEvent",
        "TaskStartedEvent",
        "TaskCompletedEvent",
        "LLMCallStartedEvent",
        "LLMStreamChunkEvent",
        "LLMCallCompletedEvent",
        "ToolUsageStartedEvent",
        "ToolUsageFinishedEvent",
    ],

    # 要忽略的事件类型
    "ignored_events": [
        "MemoryQueryStartedEvent",
        "MemoryQueryCompletedEvent",
    ]
}

# 显示格式配置
DISPLAY_FORMAT = {
    # 分隔符
    "separator_char": "=",
    "separator_length": 80,
    "sub_separator_char": "-",
    "sub_separator_length": 60,

    # 图标配置
    "icons": {
        "crew_start": "🚀",
        "crew_complete": "✅",
        "agent_start": "🤖",
        "agent_complete": "✅",
        "task_start": "📋",
        "task_complete": "✅",
        "llm_thinking": "🧠",
        "llm_response": "💭",
        "tool_start": "🔧",
        "tool_complete": "✅",
        "final_report": "📊",
        "fire": "🔥",
        "target": "🎯",
        "celebration": "🎉"
    }
}
